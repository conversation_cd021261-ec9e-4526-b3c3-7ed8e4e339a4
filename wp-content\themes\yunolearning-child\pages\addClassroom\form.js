Vue.component("yuno-org-form", {
  props: {
    fields: {
      type: Array,
      required: true,
    },
    payload: {
      type: Object,
      required: true,
    },
    timeSlots: {
      type: Array,
      required: false,
    },
  },
  template: `
        <div class="yunoFormWrapper">
            <validation-observer 
                tag="div" 
                class="observer"
                ref="orgSettingsFormObserver" 
                v-slot="{ handleSubmit, invalid }"
            >
                <form @submit.prevent="handleSubmit(initForm)">
                    <template v-for="(field, i) in fields">
                        <template v-if="field.type === 'text'">
                            <b-field :label="field.label" :key="i">
                                <validation-provider 
                                    tag="div" 
                                    class="fieldWrapper" 
                                    :rules="{
                                        required: field.isRequired
                                    }" 
                                    v-slot="{ errors, classes }"
                                >
                                    <b-input
                                        :class="classes"
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                        :readonly="field?.readonly"
                                    >
                                    </b-input>
                                    <p class="error">{{errors[0]}}</p>
                                </validation-provider>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'textarea'">
                            <b-field :label="field.label" :key="i">
                                <b-input
                                    type="textarea"
                                    v-model="payload[field.name]"
                                    :placeholder="field.placeholder"
                                    :disabled="field.disabled"
                                    :loading="field.isLoading"
                                >
                                </b-input>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'checkbox'">
                            <b-field :key="i">
                                <b-checkbox 
                                    :value="payload[field.name]" 
                                    v-model="payload[field.name]"
                                >
                                    {{ field.label}}
                                </b-checkbox>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'dropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                        @input="dropdownChange($event)"
                                    >
                                        <option
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                            :value="option.value"
                                            :disabled="option.isDisabled"
                                        >
                                            {{ option.label }}
                                        </option>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'googleFontFamilydropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="payload[field.name]"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                    >
                                        <option
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                            :value="option.family"
                                        >
                                            {{ option.family }}
                                        </option>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'multiSelectDropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <validation-provider 
                                        tag="div" 
                                        class="fieldWrapper" 
                                        :rules="{
                                            required: field.isRequired
                                        }" 
                                        v-slot="{ errors, classes }"
                                    >
                                        <b-dropdown
                                            v-model="field.selected"
                                            :class="classes"
                                            multiple
                                            :disabled="field.disabled"
                                            aria-role="list"
                                            @change="multiSelectDropdownChange($event, field)"
                                        >
                                            <template #trigger>
                                                <b-button>
                                                    <template v-if="field.selected.length">
                                                        <div class="selected">
                                                            <template v-for="(subOption, k) in field.selected">
                                                                {{ subOption.name }}{{ k < field.selected.length - 1 ? ', ' : '' }}
                                                            </template>    
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="placeholder">{{ field.placeholder }}</div>
                                                    </template>
                                                    <div class="material-icons">expand_more</div>    
                                                </b-button>
                                            </template>
                                            <b-dropdown-item 
                                                :value="subOption"
                                                aria-role="listitem"
                                                v-for="(subOption, k) in field.options"
                                                :key="k"
                                            >
                                                <span>{{ subOption.name }}</span>
                                            </b-dropdown-item>
                                        </b-dropdown>
                                        <p class="error">{{errors[0]}}</p>
                                    </validation-provider>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'groupDropdown'">
                            <b-field :label="field.label" :key="i">
                                <template v-if="field.loading">
                                    <b-skeleton height="40px"></b-skeleton>
                                </template>
                                <template v-else>
                                    <b-select
                                        v-model="industry"
                                        :placeholder="field.placeholder"
                                        :disabled="field.disabled"
                                        :loading="field.isLoading"
                                        @input="groupDropdownChange($event, field)"
                                    >
                                        <optgroup 
                                            :label="option.label"
                                            v-for="(option, j) in field.options"
                                            :key="j"
                                        >
                                            <option
                                                v-for="(subOption, k) in option.sub_industry"
                                                :key="k"
                                                :value="subOption"
                                            >
                                                {{ subOption.label }}
                                            </option>
                                        </optgroup>
                                    </b-select>
                                </template>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'upload'">
                            <b-field :label="field.label" :key="i" class="uploadField">
                                <p class="helper">{{ field.placeholder }}</p>
                                <b-upload
                                    v-model="payload[field.name]"
                                    accept="image/*"
                                    class="file-label"
                                >
                                    <span class="file-cta">
                                        <span class="material-icons-outlined">file_upload</span>
                                        <span class="file-label">{{ field.cta }}</span>
                                    </span>
                                    <span class="file-name" v-if="payload[field.name]">
                                        {{ payload[field.name].name }}
                                    </span>
                                </b-upload>
                            </b-field>
                        </template>
                        <template v-if="field.type === 'colorpicker'">
                            <b-field :label="field.label" :key="i">
                                <b-colorpicker
                                    :color-formatter="(color) => color.toString('hex')"
                                    representation="square"
                                    @input="updateColor($event, field.name)"
                                    :value="payload[field.name]"
                                    :disabled="field.disabled"
                                    :loading="field.isLoading"
                                >
                                </b-colorpicker>
                            </b-field>
                        </template>
                        <template v-if="field?.isGrouped">
  							<h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>
							<div :class="field?.class" :key="i">
								<template v-for="(input, j) in field.fields">
									<template v-if="input.type === 'text'">
										<div :class="input?.class" :key="j">
											<b-field :label="input.label">
												<validation-provider 
													tag="div" 
													class="fieldWrapper" 
													:rules="{
														required: input.isRequired
													}" 
													v-slot="{ errors, classes }"
												>
													<b-input
														:class="classes"
														class="checkbox11"
														v-model="payload[input.name]"
														:placeholder="input.placeholder"
														:disabled="input.disabled"
														:loading="input.isLoading"
													>
													</b-input>
													<p class="error">{{errors[0]}}</p>
												</validation-provider>
											</b-field>
										</div>
									</template>
									<template v-if="input.type === 'checkbox'">
  										<div :class="input?.class" :key="j">
											<b-field>
												<b-checkbox 
													:value="payload[input.name]" 
													v-model="payload[input.name]"
												>
													{{ input.label}}
												</b-checkbox>
											</b-field>
										</div>
									</template>
									<template v-if="input.type === 'dropdown'">
										<b-field :label="input.label" :key="i">
											<template v-if="input.loading">
												<b-skeleton height="40px"></b-skeleton>
											</template>
											<template v-else>
												<b-select
													v-model="payload[input.name]"
													:placeholder="input.placeholder"
													:disabled="field.disabled"
													:loading="input.isLoading"
													@input="dropdownChange($event)"
												>
													<option
														v-for="(option, j) in input.options"
														:key="j"
														:value="option.value"
														:disabled="option.isDisabled"
													>
														{{ option.label }}
													</option>
												</b-select>
											</template>
										</b-field>
                        			</template>
								</template>
							</div>
                        </template>
                        <template v-if="field.type === 'timeSlots'">
                            <h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>
                            <yuno-availability-v2
                                :data="timeSlots"
                            >
                            </yuno-availability-v2>
                        </template>
						<template v-if="field.type ==='classDetailForm'">
							<h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>
							<template v-for="(classroom, classroomIndex) in field.classrooms">
								<div :class="field?.class" :key="i + '-' + classroomIndex" >
									<div class="deleteClass" v-if="classroomIndex > 0" @click="removeClassroom(field,classroomIndex)">
										<span class="material-icons">close</span>
									</div>
									<div :class="classroom?.isGrouped ? 'grouped' : ''">
  										<template v-for="(input, j) in classroom.fields">
											<template v-if="input.type === 'text'">
												<div class="mb-2" :key="input.name + j">
													<b-field :label="input.label">
														<validation-provider 
															tag="div" 
															class="fieldWrapper" 
															:rules="{
																required: input.isRequired
															}" 
															v-slot="{ errors, classes }"
														>
															<b-input
																:class="classes"
																v-model="payload[input.name + '_' + classroomIndex]"
																:placeholder="input.placeholder"
																:disabled="input.disabled"
																:loading="input.isLoading"
															>
															</b-input>
															<p class="error">{{errors[0]}}</p>
														</validation-provider>
													</b-field>
												</div>
											</template>
											<template v-if="input.type === 'dropdown'">
												<b-field :label="input.label" >
													<template v-if="input.loading">
														<b-skeleton height="40px"></b-skeleton>
													</template>
													<template v-else>
														<b-select
															:key="i"
															v-model="payload[input.name + '_' + classroomIndex]"
															:placeholder="input.placeholder"
															:disabled="field.disabled"
															:loading="input.isLoading"
															@input="dropdownChange($event)"
														>
															<option
																v-for="(option, j) in input.options"
																:key="option.label + '_' + j"
																:value="option.value"
																:disabled="option.isDisabled"
															>
																{{ option.label }}
															</option>
														</b-select>
													</template>
												</b-field>
											</template>
                                            <template v-if="input.type === 'groupedTextDropdown'">
												<div :class="input?.class" :key="j">
													<template v-for="(group, k) in input.groups">
														<template v-if="group.type === 'text'">
  															<b-field :label="group.label" :key="k">
																<validation-provider 
																	tag="div" 
																	class="fieldWrapper" 
																	:rules="{
																		required: group.isRequired
																	}" 
																	v-slot="{ errors, classes }"
																>
																	<b-input
																		:class="classes"
																		v-model="payload[group.name + '_' + classroomIndex]"
																		:placeholder="group.placeholder"
																		:disabled="group.disabled"
																		:loading="group.isLoading"
																	>
																	</b-input>
																	<p class="error">{{errors[0]}}</p>
																</validation-provider>
															</b-field>
														</template>
														<template v-if="group.type === 'dropdown'">
															<b-field :label="group.label" :key="k">
																<b-select
																	v-model="payload[group.name + '_' + classroomIndex]"
																	:placeholder="group.placeholder"
																	:disabled="group.disabled"
																	:loading="group.isLoading"
																	@input="dropdownChange($event)"
																>
																	<option
																		v-for="(option, j) in group.options"
																		:key="option.label + '_' + j"
																		:value="option.value"
																		:disabled="option.isDisabled"
																	>
																		{{ option.label }}
																	</option>
																</b-select>
															</b-field>
														</template>
													</template>
												</div>
											</template>
											<template v-if="input.type === 'gridCheckbox'">
												<h2 class="subtitle1 onSurface mt-3" >{{ input?.title}}</h2>
												<div :class="input.class" :key="j">
													<template v-for="(checkbox,k) in input.fields">
														<div :key="checkbox.name + '_'  + k">
															<b-field>
																<b-checkbox 
																	v-model="payload[checkbox.name + '_' + classroomIndex]"
																>
																	{{ checkbox.label}}
																</b-checkbox>
															</b-field>
														</div>
													</template>
												</div>
											</template>
										</template>
									</div>
								</div>
							</template>
							<div class="primaryColor subtitle2" @click="addNewClassroom(field)">
								<span>+</span>
								<span class="addClass">Add more classroom</span>
							</div>
						</template>
                    </template>
                    <div class="ctaWrapper">
                        <b-button
                            native-type="reset"
                            @click="clearForm"
                            class="yunoPrimaryCTA wired fat">
                            Cancel
                        </b-button>    
                        <b-button
                            native-type="submit"
                            :loading="form.isLoading"
                            :disabled="form.isLoading"
                            class="yunoPrimaryCTA fat">
                            Submit
                        </b-button>    
                    </div>
                </form>
            </validation-observer>
        </div>
    `,
  data() {
    return {
      industry: null,
      defaultPayload: [],
      classrooms: [],
    };
  },
  computed: {
    ...Vuex.mapState(["user", "categoryTaxonomy", "form"]),
  },
  watch: {
    industryData: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.industry = newValue;
        }
      },
      deep: true,
    },
  },
  async created() {},
  destroyed() {},
  mounted() {
    this.init();
  },
  methods: {
    /**
     * Updates the color value in the form payload.
     * @param {Color} color - The color value to be updated.
     * @param {string} field - The field in the form payload to update.
     */
    updateColor(color, field) {
      this.form.payload[field] = color.toString("hex");
    },
    /**
     * Initializes the form.
     */
    init() {
      this.defaultPayload = JSON.parse(JSON.stringify(this.form.payload));
    },
    /**
     * Clears the form by resetting the form observer and setting the payload to the default payload.
     */
    clearForm() {
      this.$refs.orgSettingsFormObserver.reset();
      this.form.payload = JSON.parse(JSON.stringify(this.defaultPayload));
    },
    /**
     * Converts a JSON object to FormData.
     *
     * @param {Object} json - The JSON object to convert.
     * @returns {FormData} The converted FormData object.
     */
    jsonToFormData(json) {
      const formData = new FormData();
      for (let key in json) {
        if (
          Array.isArray(json[key]) &&
          json[key].every((item) => typeof item === "object")
        ) {
          formData.append(key, JSON.stringify(json[key]));
        } else {
          formData.append(key, json[key]);
        }
      }
      return formData;
    },
    /**
     * Initializes the form and emits the 'submitForm' event with the form data.
     * @memberof ClassName
     * @function initForm
     */
    initForm() {
      this.$emit("submitForm", this.jsonToFormData(this.form.payload));
    },
    dropdownChange(data) {},
    /**
     * Handles the change event of the group dropdown.
     * Updates the form payload based on the selected field.
     *
     * @param {Object} parent - The parent value of the selected option.
     * @param {string} slug - The slug value of the selected option.
     * @param {Object} field - The field object representing the selected option.
     */
    groupDropdownChange({ parent, slug }, field) {
      if (field.name === "sub_industry") {
        this.form.payload.industry = parent;
        this.form.payload.sub_industry = slug;
      }
    },
    multiSelectDropdownChange(selected, field) {
      this.form.payload[field.name] = selected;
    },
    addNewClassroom(field) {
      const newClassroom = field.classrooms[0];
      field.classrooms.push(newClassroom);
    },

    removeClassroom(field, index) {
      field.classrooms.splice(index, 1);
    },
  },
});
