@font-face {
  font-family: 'Material Icons Outlined';
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}

@font-face {
  font-family: 'FontAwesome';
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2");
}

#app, #app .field .label {
  color: #201A19;
}

#app .field.uploadField .helper {
  color: #534342;
}

#app .mainHeader h1 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .yunoFormWrapper .ctaWrapper .button, #app .field .label {
  font-size: 16px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .field.uploadField .helper {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .mainHeader {
  margin-bottom: 16px;
}

#app .mainHeader h1 {
  margin: 14px 0 0 20px;
}

@media (min-width: 768px) {
  #app .mainHeader h1 {
    margin: 0;
  }
}

#app .yunoFormWrapper {
  padding-bottom: 30px;
}

#app .yunoFormWrapper .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

#app .yunoFormWrapper .ctaWrapper .button {
  width: 100%;
}

#app .field .control input[type="text"] {
  border-color: #e6e6e6;
  height: 40px;
}

#app .field .control textarea {
  border-color: #e6e6e6;
}

#app .field .control .select {
  width: 100%;
}

#app .field .control .select select {
  border-color: #e6e6e6;
  width: 100%;
}

#app .field .control.colorpicker .button {
  height: 40px;
  border-radius: 4px;
}

#app .field.uploadField .field.has-addons {
  display: block;
}

#app .field.uploadField .upload {
  margin-top: 15px;
}

#app .field.uploadField .upload .file-cta {
  background-color: #A81E22;
  color: white;
}

#app .field.uploadField .upload .file-cta .material-icons-outlined {
  font-size: 18px;
  margin-right: 5px;
}

#app .field .dropdown {
  width: 100%;
}

#app .field .dropdown .dropdown-trigger {
  width: 100%;
}

#app .field .dropdown .button {
  width: 100%;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  border-color: #e6e6e6;
  height: 40px;
}

#app .field .dropdown .button > span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

#app .field .dropdown .button .selected {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#app .field .dropdown .button .selected span {
  margin-right: 5px;
}

#app .field .dropdown .button .selected span::after {
  content: ",";
}

#app .field .dropdown .button .selected span:last-child::after {
  content: "";
}

#app .field .dropdown .dropdown-menu {
  width: 100%;
}

#app .field .dropdown .dropdown-menu .dropdown-item:hover {
  text-decoration: none;
  background-color: #FFF8F7;
}

#app .field .dropdown .dropdown-menu .dropdown-item.is-active {
  background-color: #EDE0DE;
}

#app .field .dropdown .placeholder {
  color: #8080808c;
}

#app .field .dropdown.invalid .button {
  border-color: red;
}

#app .categoryTaxonomyWrapper {
  padding-top: 30px;
}

#app .categoryTaxonomy {
  background: #FFF;
  border: 1px solid #E6E6E6;
  border-top: 0;
}

#app .categoryTaxonomy:first-child {
  border-top: 1px solid #E6E6E6;
}

#app .categoryTaxonomy .collapse-trigger {
  display: block;
  padding: 0;
}

#app .categoryTaxonomy .collapse-trigger .b-radio {
  margin: 0;
}

#app .categoryTaxonomy .collapseHeader {
  position: relative;
}

#app .categoryTaxonomy .collapseHeader.menuDown {
  -webkit-box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
          box-shadow: rgba(0, 0, 0, 0.1) 0 4px 6px;
}

#app .categoryTaxonomy .collapseHeader .b-radio {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: 15px 15px;
}

#app .categoryTaxonomy .collapseHeader .b-radio + .error {
  display: none;
  margin: 0;
  padding: 0 0 15px 45px;
}

#app .categoryTaxonomy .collapseHeader .b-radio.invalid + .error {
  display: block;
}

#app .categoryTaxonomy .collapseHeader .fa {
  position: absolute;
  right: 15px;
  top: calc(50% - 8px);
}

#app .categoryTaxonomy .collapse-content {
  display: block;
  padding: 15px 30px;
}

#app .categoryTaxonomy .collapse {
  margin-bottom: 15px;
}

#app .categoryTaxonomy .collapse .collapse-trigger {
  position: relative;
  padding-left: 18px;
}

#app .categoryTaxonomy .collapse .collapse-trigger .fa {
  position: absolute;
  left: 0;
  top: 3px;
}

#app .categoryTaxonomy .collapse .collapse-content {
  padding-bottom: 0;
  position: relative;
}

#app .categoryTaxonomy .collapse .collapse-content:before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: #E6E6E6;
  position: absolute;
  left: 4px;
  top: 0;
}

#app .categoryTaxonomy .collapse .sub2Content .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
}

#app .categoryTaxonomy .collapse .sub2Content .field:before {
  content: "";
  width: 100%;
  height: 1px;
  background-color: #E6E6E6;
  position: absolute;
  left: -25px;
  top: 10px;
  z-index: 1;
}

#app .categoryTaxonomy .collapse .sub2Content .field:after {
  content: "";
  background: #FFF;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

#app .categoryTaxonomy .collapse .sub2Content .field .b-checkbox {
  position: relative;
  z-index: 3;
}

#app .categoryTaxonomy .collapse .trigger .field {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .categoryTaxonomy .collapse .trigger .b-checkbox {
  margin: 0;
}

.add-classroom {
  padding: 1rem 0;
  background-color: #fff;
}

.add-classroom .goBack {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  cursor: pointer;
}

.add-classroom .mainHeader {
  margin-bottom: 1.5rem;
}

.add-classroom .mainHeader h1 {
  font-size: 1.5rem;
  color: #333;
  font-weight: 500;
  margin: 0;
}

.add-classroom .classroomContainer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  width: 100%;
}

@media (min-width: 768px) {
  .add-classroom .classroomContainer {
    width: 55%;
  }
}

.add-classroom .classroomContainer.details {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  gap: 124px;
}

@media (max-width: 768px) {
  .add-classroom .classroomContainer.details {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.add-classroom .classroomContainer.details .classroomDetailForm {
  width: 100%;
}

.add-classroom .classroomContainer.details .mapWrapper {
  margin-top: 65px;
}

.add-classroom .searchLocation {
  margin-bottom: 1rem;
}

.add-classroom .searchLocation h2 {
  font-size: 0.875rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: normal;
}

.add-classroom .searchLocation .searchContainer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}

.add-classroom .searchLocation .searchContainer .field {
  width: 100%;
  margin-bottom: 0px !important;
}

@media (max-width: 576px) {
  .add-classroom .searchLocation .searchContainer {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.add-classroom .searchLocation .searchContainer .main_text {
  font-size: 16px;
  line-height: 1.7rem;
  font-weight: 500;
  color: #534342;
}

.add-classroom .searchLocation .searchContainer .secondary_text {
  font-size: 14px;
  line-height: 1.2rem;
  color: #534342;
}

.add-classroom .searchLocation .searchContainer .cta .button.yunoPrimaryCTA {
  padding: 9px 20px !important;
}

.add-classroom .open-hours-table {
  border-collapse: collapse;
  margin-top: 20px;
  border: 1px solid #e6e6e6;
}

.add-classroom .open-hours-table th,
.add-classroom .open-hours-table td {
  text-align: start;
}

.add-classroom .open-hours-table th {
  padding: 10px 23px 0 14px;
}

.add-classroom .open-hours-table td {
  padding: 1rem 23px 5px 14px;
}

.add-classroom .open-hours-table .timepicker input {
  width: 110px !important;
  padding-left: 5px !important;
}

.add-classroom .mapWrapper {
  border-radius: 4px;
  height: 350px;
  width: 100%;
  margin-top: 5px;
}

.add-classroom .infiniteSpinner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: 200px;
  margin: 0;
}

.add-classroom .infiniteSpinner img {
  opacity: 0.7;
}

.add-classroom .classDetailForm {
  position: relative;
  padding: 20px;
  margin: 20px 0;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.add-classroom .classDetailForm .classroomFloor {
  display: flex;
  gap: 15px;
  margin: 10px 0;
}
.add-classroom .classDetailForm .classroomFloor .field {
  width: 50%;
}
.add-classroom .classDetailForm .deleteClass {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.add-classroom .classDetailForm .deleteClass .material-icons {
  font-size: 24px;
}

.add-classroom .addClass {
  text-decoration: underline;
  cursor: pointer;
}

.add-classroom .yunoFormWrapper .grouped {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  margin: 20px 0;
}

.add-classroom .yunoFormWrapper .hasFlex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 10px 0;
  gap: 15px;
}

.add-classroom .yunoFormWrapper .hasFlex .field {
  width: 100%;
}

.add-classroom .yunoFormWrapper .fullWidth {
  width: 100%;
}

.add-classroom .yunoFormWrapper .hasFlexColumn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  margin: 10px 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.add-classroom .yunoFormWrapper .groupCheckbox {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: (1fr)[2];
      grid-template-columns: repeat(2, 1fr);
  gap: 2px;
  margin: 10px 0;
}

@media (max-width: 768px) {
  .add-classroom .yunoFormWrapper .groupCheckbox {
    -ms-grid-columns: (1fr)[2];
        grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .add-classroom .yunoFormWrapper .groupCheckbox {
    -ms-grid-columns: 1fr;
        grid-template-columns: 1fr;
  }
}

.add-classroom .yunoFormWrapper .groupCheckbox .b-checkbox .control-label {
  font-size: 14px;
  color: #201A19;
}

.add-classroom .yunoFormWrapper .faciltiesCheckbox {
  margin: 10px 0;
}

.add-classroom .yunoFormWrapper .faciltiesCheckbox .addMargin {
  margin: 5px 0px 5px 32px;
}

@media (max-width: 768px) {
  .add-classroom .mainHeader {
    padding: 0 1rem;
  }
  .add-classroom .searchSection {
    padding: 0 1rem;
  }
  .add-classroom .map-container {
    margin: 1rem;
  }
}

.open-hours-wrapper {
  margin-bottom: 20px;
}

.open-hours-container {
  border-radius: 4px;
}

.open-hours-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 10px;
}

.open-hours-table th {
  text-align: left;
  padding: 10px 15px;
  font-weight: normal;
  color: #000;
}

.open-hours-table td {
  padding: 5px 15px;
  vertical-align: middle;
}

.open-hours-table .day-cell {
  color: #000;
}

.open-hours-table .time-picker {
  width: 100%;
  max-width: 150px;
}

.open-hours-table .time-picker input {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  width: 100%;
}

.open-hours-table .add-button {
  cursor: pointer;
}

@media (max-width: 768px) {
  .open-hours-container {
    padding: 10px;
  }
  .open-hours-table td,
  .open-hours-table th {
    padding: 5px;
  }
  .open-hours-table .time-picker {
    max-width: none;
  }
}
/*# sourceMappingURL=addClassroom.css.map */