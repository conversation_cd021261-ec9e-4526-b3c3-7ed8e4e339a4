<?php

namespace V4;

use WP_Query;

/**
 * Batch model
 */
class BatchModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('response');
        $this->loadLibary('utility');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
    }

    public function getBatch($query, $filter = [])
    {
        ynLog("getBatch - query  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($query), 'getBatch_V4_Model');
        $query = is_array($query) ? $query : ['id' => $query];

        if (isset($query['id'])) {
            // Define the query
            $batchQuery = [
                '_source' => false,
                'query' => [
                    'nested' => [
                        'path' => 'data.details.batch_details',
                        'query' => [
                            'term' => [
                                'data.details.batch_details.batch_id' => $query['id']
                            ]
                        ],
                        'inner_hits' => (object)[]
                    ]
                ]
            ];
            ynLog("getBatch - batchQuery  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchQuery), 'getBatch_V4_Model');
            $dataResponse = $this->es->customQuery($batchQuery, 'course');
        } else {
            return false;
        }

        if ($dataResponse['status_code'] == 200) {
            $batchDataResponse = $dataResponse['body']['hits']['hits'][0];
            ynLog("getBatch - batchDataResponse  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchDataResponse), 'getBatch_V4_Model');

            // Output the response (inner_hits only)
            // Iterate over the hits
            foreach ($batchDataResponse as $hit) {
                if (isset($hit['data.details.batch_details']['hits']['hits'])) {
                    $this->loadModel('locale');
                    //foreach ($hit['inner_hits']['data.details.batch_details']['hits']['hits'] as $innerHit) {
                    $batch = $hit['data.details.batch_details']['hits']['hits'][0]['_source'];
                    ynLog("getBatch - batch  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batch), 'getBatch_V4_Model');
                    $formattedResponse = [
                        'id' => $batch['batch_id'],
                        'title' => $batch['batch_title'],
                        'temporal_state' => $batch['temporal_state'] ?? '',
                        'instructor' => $this->load->subData("user", "getUser", $batch['instructor_id'] ?? 0, ['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']),
                        'checkout_url' => $batch['batch_post_url'] ?? '',
                        'created_by' => $this->load->subData("user", "getUser", $batch['publisher_id'] ?? 0, ['schema' => 'User_Minimal', 'noResponse' => 'User_Minimal']),
                        'is_active' => $batch['active_batch'],
                        'is_locked' => $batch['locked_batch'],
                        'is_deleted' => $batch['batch_deleted_status'],
                        'start_end' => [
                            'start_date' => array(
                                'time' => $this->dt->convertToSystemDT($batch['batch_start_date']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()  // Timezone 
                            ),
                            'end_date' => array(
                                'time' => $this->dt->convertToSystemDT($batch['batch_end_date']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()  // Timezone 
                            ),
                            'display_start' => array(
                                'time' => $this->dt->convertToSystemDT($batch['batch_display_start_date']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()  // Timezone 
                            ),
                            'display_end' => array(
                                'time' => $this->dt->convertToSystemDT($batch['batch_display_end_date']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()  // Timezone 
                            )
                        ],
                        'class_days' => array_map(function ($day) {
                            return [
                                'day' => $day ?? null,
                                'name' => null,
                                'available' => true
                            ];
                        }, $batch['days_of_week'] ?? []),
                        'class_time' => [
                            'name' => '' . ($batch['class_time'] ?? 'MORNING') . '"', // Time is grouped in four values: morning, afternoon, evening and night enum ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT']
                            'start_time' => [
                                'time' => $this->dt->convertToSystemDT($batch['class_start_time']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()
                            ], // Timezone 
                            'end_time' => [
                                'time' => $this->dt->convertToSystemDT($batch['class_end_time']) ?? '',  // Start date // DateTime Missing
                                'timezone' => $this->locale->activeTimezone()
                            ], // Timezone 
                            'duration' => $batch['duration']
                        ],
                        'personalisation' => $batch['personalisation'],
                        'teaching_mode' => [
                            'online' => strtolower($batch['teaching_mode'] ?? '') === 'online',
                            'in_person' => strtolower($batch['teaching_mode'] ?? '') === 'in_person'
                        ],
                        'seats' => [
                            'max' => $batch['max_seats'] ?? 0,
                            'occupied' => $batch['enrollment_counter'] ?? 0,
                            'vacant' => ($batch['max_seats'] ?? 0) - ($batch['enrollment_counter'] ?? 0)
                        ],
                        'in_crm' => [
                            'platform' => '',
                            'batch_id' => $batch['zoho_batch_id'] ?? 0
                        ],
                        'course' => $this->load->subData("course", "getCourse", $batch['course_id'], ['schema' => 'Course_Minimal', 'noResponse' => 'Course_Minimal']),
                        'active_enrollments' => array_map(function ($enrollment) {
                            // return [
                            //     'enrollment_id' => $enrollment['id'] ?? 0,
                            //     'learner_name' => $enrollment['learner_name'] ?? ''
                            // ];
                            return $this->load->subData("enrollment", "getEnrollment", $enrollment['id'], ['schema' => 'Enrollment_Minimal', 'noResponse' => 'Enrollment_Minimal']);
                        }, $batch['active_enrollments'] ?? []),
                        'place' => $this->load->subData("place", "getPlace", $batch['place']['id'], ['schema' => 'Place_Minimal', 'noResponse' => 'Place_Minimal']),
                        'classroom' => $this->load->subData("classroom", "getClassroom", $batch['classroom']['id'], ['schema' => 'Classroom_Minimal', 'noResponse' => 'Classroom_Minimal']),
                        'is_enrollable' => $batch['is_enrollable'] ?? false, // Whether the batch is enrollable or not
                        'has_vacancy' => $batch['vacancy'] > 0 ? true : false,
                    ];
                    ynLog("getBatch - formattedResponse  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($formattedResponse), 'getBatch_V4_Model');
                }
            }

            ynLog("getBatch - formattedResponse afterrr " . date("Y-m-d H:i:s") . " === ID: " . json_encode($formattedResponse), 'getBatch_V4_Model');
            return $this->schema->validate($formattedResponse, 'Batch', $filter);
        }
        return false;
    }

    // Get batch details
    public function getBatchSQL($batch_id)
    {
        global  $wpdb;
        $wp_batches = $wpdb->get_row("SELECT * FROM wp_batches WHERE id = $batch_id", ARRAY_A);
        if (!$wp_batches) {
            // Return error if an exception occurs
            return false;
        }
        return $wp_batches;
    }

    // Check if the batch is locked and validate enrollment
    // public function validateBatchLock($wpBatches)
    // {
    //     global $wpdb;
    //     if (!empty($wpBatches['batch_reference_post_id'])) {
    //         $isBatchLocked = get_post_meta($wpBatches['batch_reference_post_id'], 'locked_batch', true);
    //         if ($isBatchLocked == 1) {
    //             return false;
    //         }
    //         return true;
    //     }
    // }

    /**
     * Validates if the batch enrollment is allowed based on the batch end date and course duration.
     *
     * @param array $wpBatches The batch details retrieved from the database or custom post type.
     * @param array $request The request payload containing enrollment details.
     * @return bool|WP_Error Returns true if validation passes, or WP_Error if validation fails.
     */
    /**
     * Validates if batch enrollment is allowed based on the batch end date and course duration.
     *
     * @param array $batchDetails The batch details retrieved from the database or custom post type.
     * @param array $requestData The request payload containing enrollment details.
     * @return bool Returns true if validation passes, false otherwise.
     */
    public function verifyBatchEnrollment($batchDetails, $batchId)
    {
        global $wpdb;

        $batchEndDate = $batchDetails['end_date'];
        $enrollmentType = $batchDetails['enrollment_type'];

        // Retrieve courses associated with the batch
        $courses = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT DISTINCT product_id FROM {$wpdb->prefix}group_relationships WHERE batch_id = %d",
                $batchId
            )
        );

        $courseDurations = array_map(function ($course) {
            $durationWeeks = (int) get_post_meta($course->product_id, 'Duration_Weeks', true);
            return $durationWeeks * 7; // Convert weeks to days
        }, $courses);

        // Determine the maximum course duration
        $maxDurationInDays = !empty($courseDurations) ? max($courseDurations) : 0;
        $calculatedDate = date('Y-m-d H:i:s', strtotime("+$maxDurationInDays days"));


        // Validate batch enrollment based on calculated end date
        if ($batchEndDate < $calculatedDate) {
            return false; // Validation failed
        }

        return true; // Validation passed
    }

    public function addBatchToCourse($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (file_exists(get_stylesheet_directory() . '/inc/classes/rest_api/v1/CSVController.php')) {
            include_once(get_stylesheet_directory() . '/inc/classes/rest_api/v1/CSVController.php');

            if (class_exists('CSVController')) {
                echo "Class CSVController found.";
                $csvController = new \CSVController();
                $csvController->course_batch($batch_id);
            } else {
            }
        } else {
        }
    }

    /**
     * Generates the batch filter for an instructor.
     *
     * Builds a filter array for batch selection by retrieving batch details if provided
     * and fetching active batches for the given instructor. The returned array is used
     * to populate a dropdown UI for selecting a batch.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $batch The batch identifier or details.
     * @param int $instructorId The instructor's ID.
     * @return array The filter array for batch selection.
     * <AUTHOR>
     */
    public function generateBatchFiltersInstructor($batch, $instructorId)
    {
        $row = [
            'filter' => 'batch',
            'title' => 'batch',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Batch',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($batch)) {
            $batch_data = $this->getBatch($batch, ['schema' => 'Batch_Minimal']);

            if ($batch_data) {
                $batch_name = $batch_data['title'] ?? '';
                $sel_batch_id = $batch_data['id'] ?? 0;

                $selected_batch = [
                    'id' => $sel_batch_id,
                    'label' => $batch_name,
                    'filter' => 'batch',
                ];
                $row['selected'] = (int)$selected_batch['id'];
            }
        }

        $activeBatches = $this->utility->getActiveBatchesInstructor($instructorId);

        if (!empty($activeBatches)) {
            $row['items'] = $activeBatches;
        }
        return $row;
    }

    /**
     * Generates batch filter for a learner.
     *
     * Retrieves the selected batch (if provided) and fetches available batches from enrollment events
     * based on the learner's ID, then constructs a dropdown filter for batch selection.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $batch The batch identifier or details.
     * @param int $learnerId The learner's ID.
     * @return array|false The filter array or false on failure.
     * <AUTHOR>
     */
    public function generateBatchFiltersLearner($batch, $learnerId)
    {
        $row = [
            'filter' => 'batch',
            'title' => 'batch',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Batch',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];
        if ($batch) {
            $batch_data = $this->getBatch($batch, ['schema' => 'Batch_Minimal']);
            if ($batch_data) {
                $row['selected'] = (int)($batch_data['id'] ?? 0);
            }
        }
        $customQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.user_id' => $learnerId
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $batchRecords = $this->es->customQuery($customQuery, 'batchenrollmentevent', []);

        if ($batchRecords['status_code'] == 200) {
            $batches = $batchRecords['body']['hits']['hits'];
        } else {
            return false;
        }
        $existingBatchIds = [];
        if (!empty($batches)) {
            foreach ($batches as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $batchId = $details['batch_id'] ?? 0;
                $batchName = $details['batch_name'] ?? '';

                if ($batchId && !in_array($batchId, $existingBatchIds)) {
                    $row['items'][] = [
                        'id' => $batchId,
                        'label' => $batchName,
                        'filter' => 'batch',
                    ];
                    $existingBatchIds[] = $batchId;
                }
            }
        }

        return $row;
    }

    public function createBatch($data)
    {
        // 1. Insert as CPT (WordPress post)
        $postarr = [
            'post_title'   => $data['title'],
            'post_type'    => 'batch',
            'post_status'  => 'publish',
            'post_author'  => $data['created_by'],
            'meta_input'   => [
                'course_id'            => $data['course_id'],
                'instructor_id'        => $data['instructor_id'],
                'is_locked'            => $data['is_locked'],
                'start_date'           => $data['start_date'],
                'end_date'             => $data['end_date'],
                'class_days'           => $data['class_days'],
                'class_time'           => $data['class_time'],
                'personalisation_type' => $data['personalisation_type'],
                'teaching_mode'        => $data['teaching_mode'],
                'place'                => $data['place'],
                'classroom'            => $data['classroom'],
                'seats_max'            => $data['seats_max'],
                'created_at'           => date('Y-m-d H:i:s'),
            ]
        ];

        $batch_id = wp_insert_post($postarr);

        if (is_wp_error($batch_id) || !$batch_id) {
            return false;
        }

        // 2. Prepare data for ES
        $es_data = [
            'id'                   => $batch_id,
            'title'                => $data['title'],
            'course_id'            => $data['course_id'],
            'instructor_id'        => $data['instructor_id'],
            'created_by'           => $data['created_by'],
            'is_locked'            => $data['is_locked'],
            'start_date'           => $data['start_date'],
            'end_date'             => $data['end_date'],
            'class_days'           => $data['class_days'],
            'class_time'           => $data['class_time'],
            'personalisation_type' => $data['personalisation_type'],
            'teaching_mode'        => $data['teaching_mode'],
            'place'                => $data['place'],
            'classroom'            => $data['classroom'],
            'seats_max'            => $data['seats_max'],
            'created_at'           => date('Y-m-d H:i:s'),
        ];

        // 3. Insert into Elasticsearch
        $es_result = $this->indexBatchToES($es_data);

        if ($es_result !== true) {
            // Optionally: rollback CPT insert or log error
            return false;
        }

        return true;
    }
    /**
     * Indexes batch data into Elasticsearch
     * 
     * @param array $batchData The batch data to be indexed
     * @return bool Returns true if indexing was successful, false otherwise
     */
    private function indexBatchToES($batchData)
    {
        try {
            $courseId = $batchData['course_id'];
            $batchObject = [
                'batch_id' => $batchData['id'],
                'batch_title' => $batchData['title'],
                'temporal_state' => 'active',
                'instructor_id' => $batchData['instructor_id'],
                'instructor_name' => '', // Fetch if needed
                'instructor_profile_url' => '', // Fetch if needed
                'batch_post_url' => get_permalink($batchData['id']),
                'created_by' => [
                    'id' => $batchData['created_by'],
                    'name' => '' // Fetch if needed
                ],
                'active_batch' => true,
                'locked_batch' => $batchData['is_locked'],
                'batch_deleted_status' => false,
                'batch_start_date' => $batchData['start_date'],
                'batch_end_date' => $batchData['end_date'],
                'batch_display_start_date' => $batchData['start_date'],
                'batch_display_end_date' => $batchData['end_date'],
                'days_of_week' => $batchData['class_days'],
                'class_start_time' => $batchData['class_time']['start_time'] ?? null,
                'class_end_time' => $batchData['class_time']['end_time'] ?? null,
                'duration' => $batchData['class_time']['duration'] ?? null,
                'personalisation' => $batchData['personalisation_type'],
                'mode' => $batchData['teaching_mode'],
                'max_seats' => $batchData['seats_max'],
                'enrollment_counter' => 0,
                'zoho_batch_id' => null,
                'course_id' => $batchData['course_id'],
                'active_enrollments' => [],
                'place' => [
                    'id' => $batchData['place']['id'] ?? null,
                    'address' => $batchData['place']['address'] ?? null
                ],
                'classroom' => [
                    'id' => $batchData['classroom']['id'] ?? null,
                    'name' => $batchData['classroom']['name'] ?? null
                ],
                'is_enrollable' => true,
                'vacancy' => $batchData['seats_max']
            ];

            // Try to get the existing course document
            $existingDoc = $this->es->read('course', $courseId);

            $batchDetails = [];
            $exists = isset($existingDoc['body']['_source']);

            if ($exists) {
                // Document exists, get current batch_details array
                $source = $existingDoc['body']['_source'];
                $batchDetails = $source['data']['details']['batch_details'] ?? [];
                // Ensure it's an array
                if (!is_array($batchDetails)) {
                    $batchDetails = [$batchDetails];
                }
                $batchDetails[] = $batchObject;

                // Update the document
                $response = $this->es->update(
                    'course',
                    $courseId,
                    [
                        'data' => [
                            'details' => [
                                'batch_details' => $batchDetails
                            ]
                        ]
                    ]
                );
            } else {
                // Document does not exist, create new with batch_details as array
                $response = $this->es->create(
                    'course',
                    [
                        'data' => [
                            'details' => [
                                'batch_details' => [$batchObject]
                            ]
                        ]
                    ],
                    $courseId
                );
            }

            // Check if the indexing/updating was successful
            if (
                (isset($response['status_code']) && ($response['status_code'] === 201 || $response['status_code'] === 200)) ||
                (isset($response['result']) && in_array($response['result'], ['created', 'updated']))
            ) {
                return true;
            }

            ynLog('Failed to index batch in Elasticsearch: ' . json_encode($response), 'batch_indexing');
            return false;
        } catch (\Exception $e) {
            ynLog('Exception while indexing batch in Elasticsearch: ' . $e->getMessage(), 'batch_indexing');
            return false;
        }
    }


    /**
     * Create the custom post type 'batch'.
     */
    public function createBatchPost($data)
    {
        global $wpdb;
        ynLog("createBatchPost - input: " . json_encode($data), 'Batch_Model_createBatchPost');

        $postTitle = sanitize_text_field($data['title']);

        ynLog("createBatchPost - input: " . json_encode($data), 'Batch_Model_createBatchPost');

        // Step 1: Check for duplicate batch title
        $existing = new WP_Query([
            'post_type'      => 'batch',
            'post_status'    => 'any',
            'posts_per_page' => 1,
            'title'          => $postTitle
        ]);

        if ($existing->have_posts()) {
            ynLog("createBatchPost - duplicate batch title: $postTitle", 'Batch_Model_createBatchPost');
            return 'duplicate_title';
        }

        $postId = wp_insert_post([
            'post_title'   => $postTitle,
            'post_type'    => 'batch',
            'post_status'  => 'publish',
            'post_author'  => (int) $data['user_id']
        ]);

        if (is_wp_error($postId)) {
            ynLog("createBatchPost - failed: " . $postId->get_error_message(), 'Batch_Model_createBatchPost');
            return false;
        }
        ynLog("createBatchPost - created post ID: $postId", 'Batch_Model_createBatchPost');
        // Save all relevant post meta
        $this->saveBatchMeta($postId, $data);
        return $postId;
    }

    /**
     * Save batch metadata.
     *
     * @param int $postId The post ID of the batch.
     * @param array $data The data to save as metadata.
     * @return bool True on success, false on failure.
     */
    public function saveBatchMeta($postId, $data)
    {
        ynLog("saveBatchMeta - for post ID $postId", 'saveBatchMeta');
        ynLog("saveBatchMeta - input data: " . json_encode($data), 'saveBatchMeta');

        // Normalize input
        $courseIds        = is_array($data['course_id']) ? $data['course_id'] : [$data['course_id']];
        $courseId         = $courseIds[0];
        $instructorId     = (int) $data['instructor_id'];
        $createdBy        = (int) $data['created_by'];
        $startDateInput   = $data['start_date'];
        $endDateInput     = $data['end_date'];
        $classTimeInput   = $data['class_time'];
        $duration         = (int) $data['duration'];
        $classDays        = is_array($data['class_days']) ? $data['class_days'] : [$data['class_days']];
        $classDaysString  = implode(',', $classDays);
        $personalisation  = $data['personalisation'] ?? 'one_to_many';
        $vacancy          = (int) ($data['seats_max'] ?? 0);
        $isLocked         = (bool) ($data['is_locked'] ?? false);
        $teachingMode     = $data['teaching_mode'] ?? 'online';
        $enrollmentType   = $personalisation === 'one_to_many' ? 'fixed' : 'rolling';
        $seatsMax         = $vacancy;

        // Fetch price from course meta
        $batchPrice = get_post_meta($courseId, 'Unit_Price', true);

        // Determine batch type
        $eventStartHour = date("H", strtotime($classTimeInput));
        $batchType = match (true) {
            $eventStartHour < 12 => "morning",
            $eventStartHour < 14 => "afternoon",
            $eventStartHour < 20 => "evening",
            default => "night"
        };

        // Format dates and times
        $startDate     = date("Y-m-d", strtotime($startDateInput));
        $endDate       = date("Y-m-d", strtotime($endDateInput));
        $classTime     = date("H:i", strtotime($classTimeInput));
        $startDateTime = "$startDate $classTime:00";
        $endDateTime   = "$endDate $classTime:00";

        // Save meta
        update_post_meta($postId, 'course_id', $courseId);
        update_post_meta($postId, 'instructor_id', $instructorId);
        update_post_meta($postId, 'created_by', $createdBy);
        update_post_meta($postId, 'start_date', $startDate);
        update_post_meta($postId, 'end_date', $endDate);
        update_post_meta($postId, 'class_time', $classTime);
        update_post_meta($postId, 'duration', $duration);
        update_post_meta($postId, 'class_days', $classDaysString);
        update_post_meta($postId, 'personalisation', $personalisation);
        update_post_meta($postId, 'teaching_mode', $teachingMode);
        update_post_meta($postId, 'vacancy', $vacancy);
        update_post_meta($postId, 'is_locked', $isLocked);
        update_post_meta($postId, 'is_active', true);
        update_post_meta($postId, 'enrollment_type', $enrollmentType);
        update_post_meta($postId, 'seats_max', $seatsMax);
        update_post_meta($postId, 'batch_type', $batchType);
        update_post_meta($postId, 'batch_visibility', 0);
        update_post_meta($postId, 'batch_deleted_status', 0);
        update_post_meta($postId, 'batch_price', $batchPrice);

        // Save batch start and end datetime
        update_post_meta($postId, 'batch_start_datetime', $startDateTime);
        update_post_meta($postId, 'batch_end_datetime', $endDateTime);

        if ($teachingMode === 'in_person') {
            update_post_meta($postId, 'place_id', (int)($data['place_id'] ?? 0));
            update_post_meta($postId, 'classroom_id', (int)($data['classroom_id'] ?? 0));
        }

        ynLog("saveBatchMeta - metadata saved for post ID $postId", 'saveBatchMeta');

        $this->addBatchEs($postId);
        ynLog("saveBatchMeta - batch indexed in Elasticsearch for post ID $postId", 'saveBatchMeta');

        return true;
    }

    /**
     * Add batch to Elasticsearch course document.
     */
    public function addBatchEs($batchId)
    {
        ynLog("addBatch - processing batch ID $batchId", 'Batch_Model_addBatch');
        $allMeta = get_post_meta($batchId);

        foreach ($allMeta as $metaKey => $metaValue) {
            ynLog("Meta key: $metaKey | Value: " . json_encode($metaValue), 'Batch_Model_addBatch');
        }

        // Basic batch data from post meta
        // $courseId        = get_post_meta($batchId, 'course_id', true);
        $courseIds = (array) get_post_meta($batchId, 'course_id');
        ynLog("addBatch - course ID: $courseIds", 'Batch_Model_addBatch');
        $instructorId    = (int) get_post_meta($batchId, 'instructor_id', true);
        $createdBy       = (int) get_post_meta($batchId, 'created_by', true);
        $startDate       = get_post_meta($batchId, 'batch_start_datetime', true); // full datetime
        $endDate         = get_post_meta($batchId, 'batch_end_datetime', true);   // full datetime
        $classTime       = get_post_meta($batchId, 'class_time', true);           // only time
        $duration        = (int) get_post_meta($batchId, 'duration', true);
        $groupType       = get_post_meta($batchId, 'personalisation', true);      // 'one_to_one' or 'one_to_many'
        $teachingMode    = get_post_meta($batchId, 'teaching_mode', true);
        $vacancy         = (int) get_post_meta($batchId, 'vacancy', true);
        //$enrollmentType  = get_post_meta($courseId, 'enrollment_type', true);     // inherited
        $maxSeats        = (int) get_post_meta($batchId, 'seats_max', true);
        $daysOfWeek      = explode(',', get_post_meta($batchId, 'class_days', true));
        $batchPostUrl    = get_permalink($batchId);
        $batchPrice      = (float) get_post_meta($batchId, 'batch_price', true);

        // Calculate batch_type based on classTime
        $hour = date("H", strtotime($classTime));
        $batchType = match (true) {
            $hour < 12 => "morning",
            $hour < 14 => "afternoon",
            $hour < 20 => "evening",
            default    => "night"
        };

        // Class end time calculation
        $classEndTime = date('H:i:s', strtotime($classTime . " + {$duration} minutes"));

        // Instructor data from ES
        $instructorDetails = $this->es->read('signedup', 'signedup-' . $instructorId, ['_source' => 'data.details.user']);
        $instructorImage = $instructorDetails['body']['_source']['data']['details']['user']['image'] ?? '';
        $instructorName = $instructorDetails['body']['_source']['data']['details']['user']['name'] ?? '';
        $profilePostId  = get_user_meta($instructorId, 'profile_user_id_reference', true);
        $instructorUrl  = $profilePostId ? get_permalink($profilePostId) : false;

        // Optional reviews (disabled)
        $overallRating = 0;
        $reviewCount = 0;

        // Display window (static 1 to 15 day window)
        $batchDisplayStartDate = date('Y-m-d H:i:s', strtotime('+1 days'));
        $batchDisplayEndDate   = date('Y-m-d H:i:s', strtotime('+15 days'));

        $batch = [
            'user_id' => $createdBy,
            'publisher_id' => $createdBy,
            'image' => $instructorImage,
            'instructor_id' => $instructorId,
            // 'course_id' => $courseId,
            'batch_id' => $batchId,
            'batch_title' => html_entity_decode(get_the_title($batchId), ENT_NOQUOTES, 'UTF-8'),
            'personalisation' => $groupType === 'one_to_one' ? 1 : 0,
            'term_id' => 0,
            'batch_start_date' => $startDate,
            'time' => $classTime,
            'duration' => $duration,
            'batch_type' => $batchType,
            'batch_name' => $batchId . '-' . sanitize_title(get_the_title($batchId)),
            'days_of_week' => $daysOfWeek,
            'last_enrollment_end_date' => $endDate,
            'batch_end_date' => $endDate,
            'instructor_name' => $instructorName,
            'instructor_profile_url' => $instructorUrl,
            'any_time' => 1,
            'batch_display_start_date' => $batchDisplayStartDate,
            'batch_display_end_date' => $batchDisplayEndDate,
            'class_start_time' => $classTime,
            'class_end_time' => $classEndTime,
            'price' => $batchPrice,
            'max_seats' => $maxSeats,
            'vacancy' => $vacancy,
            'mode' => $teachingMode === 'online' ? 0 : 1,
            'instructor_rating' => (string) $overallRating,
            'instructor_completed_enrollment' => $reviewCount,
            'ongoing_batch' => 0,
            'locked_batch' => 0,
            'enrollable_status' => 0,
            'active_batch' => 0,
            'batch_deleted_status' => 0,
            'enrollment_start_date' => $startDate,
            'enrollment_counter' => 0,
            'batch_post_id' => $batchId,
            'batch_post_url' => $batchPostUrl,
            'batch_visibility' => 1,
            'teaching_mode' => $teachingMode
        ];

        if ($teachingMode === 'in_person') {
            $batch['place'] = [
                'id' => (int) get_post_meta($batchId, 'place_id', true),
                'address' => ''
            ];
            $batch['classroom'] = [
                'id' => (int) get_post_meta($batchId, 'classroom_id', true),
                'name' => ''
            ];
        }

        ynLog("addBatch - batch data prepared for ES: " . json_encode($batch), 'Batch_Model_addBatch');

        foreach ($courseIds as $courseId) {
            $batch['course_id'] = $courseId;
            ynLog("addBatch - processing course ID: $courseId", 'Batch_Model_addBatch');

            $existingDoc = $this->es->read('course', 'course-' . $courseId, ['_source' => 'data.details.batch_details']);
            $existingBatches = $existingDoc['body']['_source']['data']['details']['batch_details'] ?? [];

            ynLog("addBatch - existing batches fetched from ES: " . json_encode($existingBatches), 'Batch_Model_addBatch');

            // Step 2: Remove old batch with same batch_id (if exists)
            // $filteredBatches = array_filter($existingBatches, function ($b) use ($batchId) {
            //     return $b['batch_id'] != $batchId;
            // });

            // // Step 3: Append new batch
            // $filteredBatches[] = $batch;
            // ynLog("addBatch - filtered batches after removing old batch: " . json_encode($filteredBatches), 'Batch_Model_addBatch');

            $filteredBatches = [];
            $batchExists = false;

            foreach ($existingBatches as $existingBatch) {
                if (isset($existingBatch['batch_id']) && $existingBatch['batch_id'] == $batchId) {
                    $batchExists = true;
                    continue; // skip the old batch
                }
                $filteredBatches[] = $existingBatch;
            }

            // Add the new/updated batch
            $filteredBatches[] = $batch;

            ynLog("addBatchEs - " . ($batchExists ? "Updated existing batch ID $batchId" : "Added new batch ID $batchId"), 'Batch_Model_addBatch');


            // Step 4: Build final payload
            $payload = [
                'data' => [
                    'details' => [
                        'update_event_type' => 'course',
                        'record_id' => $courseId,
                        'batch_detail_ids' => array_column($filteredBatches, 'batch_id'),
                        'batch_details' => array_values($filteredBatches) // reset keys
                    ]
                ],
                '@timestamp' => date("Y-m-d H:i:s")
            ];

            ynLog("addBatch - payload prepared for ES: " . json_encode($payload), 'addBatch');

            $response = $this->es->update('course', 'course-' . $courseId, $payload);
            ynLog("addBatch - ES response: " . json_encode($response), 'Batch_Model_addBatch');
            ynLog("syncBatchToES - ES updated via update() for batch ID $batchId", 'Batch_Model_addBatch');
            ynLog("addBatch - ES updated for batch ID $batchId", 'Batch_Model_addBatch');
            if (isset($response['status_code']) && $response['status_code'] !== 200) {
                ynLog("addBatch - Elasticsearch update failed for batch ID $batchId: " . json_encode($response), 'Batch_Model_addBatch');
                return false;
            }
        }
        ynLog("addBatch - Batch data course ID for Elasticsearch: " . json_encode($courseIds), 'Batch_Model_addBatch');
        ynLog("addBatch - Batch indexed in Elasticsearch successfully for batch ID $batchId", 'Batch_Model_addBatch');
        ynLog("addBatch - Completed indexing batch ID $batchId", 'Batch_Model_addBatch');
        return true;
    }

    /**
     * Update an existing batch post.
     */
    public function updateBatchPost($batchId, $data)
    {
        ynLog("updateBatchPost - Updating batch post ID: $batchId", 'updateBatchPost');
        ynLog("updateBatchPost - input data: " . json_encode($data), 'updateBatchPost');

        // Update post title if batch_label is present
        if (!empty($data['title'])) {
            wp_update_post([
                'ID'         => $batchId,
                'post_title' => sanitize_text_field($data['title']),
            ]);
            ynLog("updateBatchPost - post_title updated", 'updateBatchPost');
        }

        // Save all meta fields
        $this->saveBatchMeta($batchId, $data);

        ynLog("updateBatchPost - Meta fields updated for post ID: $batchId", 'updateBatchPost');

        return $batchId;
    }

    public function getBatches($query, $filter = [])
    {
        echo json_encode($query);
        die('getBatches method is deprecated. Please use getBatchesV4 instead.');
        if (isset($query['custom'])) {
            $batchCntResponse = $this->es->count('course', $query['custom']);
            if ($batchCntResponse['status_code'] == 200) {
                $batchDataResponse = $this->es->customQuery($query['custom'], 'course', $query['qryStr']);
                if ($batchDataResponse['status_code'] == 200) {
                    $batches = $batchDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        if (count($batches)) {
            $responseCount = $batchCntResponse['body']['count'];
            foreach ($batches as $batchesData) {
                $batch = $batchesData['_source']['data']['details']['batch_details'] ?? [];
                if (isset($batch['batch_id']) && $batch['batch_id']) {
                    $batchId = $batch['batch_id'];
                    $batch['id'] = $batchId;
                    $batch['enrollment_counter'] = 0; // Default value
                    if (isset($query['custom']['query']['bool']['must'][0]['term']['data.details.batch_id'])) {
                        $batch['enrollment_counter'] = $responseCount;
                    }
                    $batchesData['_source']['data']['details']['batch_details'] = $batch;
                }

            }
        }


    }
}
