Vue.component("yuno-classroom-detail", {
  template: `
        <div class="classroomDetailForm">
            <div class="goBack mb-3" @click="hideClassroomDetail">
                <span class="material-icons headline6 noBold onSurfaceVariant">arrow_back</span>
                <span class="onSurfaceVariant subtitle2">Classroom and Place Details</span>
            </div>
            <yuno-org-form
                :fields="form.fields"
                :payload="form.payload"
                :timeSlots="timeSlots.data"
                @submitForm="submitForm"
            >
            </yuno-org-form>
        </div>
    `,
  data() {
    return {
   
    };
  },
  computed: {
    ...Vuex.mapState(["form", "timeSlots", "userInfo"]),
  },
  methods: {
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    hideClassroomDetail() {
      this.$emit("hideClassroomDetail");
    },
    createClassroomPayload(payload, place_id) {
      if (!this.form.payload.classrooms) {
        this.form.payload.classrooms = [];
      }

      // Clear any existing classrooms
      this.form.payload.classrooms = [];

      // Find all classroom indices by looking for class_title fields
      const classroomIndices = [];
      for (const key in payload) {
        if (key.startsWith("class_title_")) {
          const index = key.split("_")[2];
          classroomIndices.push(index);
        }
      }

      // Create a classroom payload for each classroom
      classroomIndices.forEach((index) => {
        const classroomPayload = {
          place_id: place_id,
          title: payload[`class_title_${index}`],
          floor: {
            type: payload[`classroom_floor_type_${index}`],
            number: payload[`classroom_floor_number_${index}`],
          },
          area: payload[`classroom_area_${index}`],
          seating_capacity: payload[`seating_capacity_${index}`],
          facilities: {
			wifi: payload[`wifi_${index}`] ?? false,
			whiteboard: payload[`whiteboard_${index}`] ?? false,
			blackboard: payload[`blackboard_${index}`] ?? false,
			projector: payload[`projector_${index}`] ?? false,
			lcd_monitor: payload[`lcd_monitor_${index}`] ?? false,
			air_conditioning: payload[`air_conditioning_${index}`] ?? false,
			power_backup: payload[`power_backup_${index}`] ?? false,			
            computer_terminals: payload[`computer_terminals_${index}`],
          },
        };
        this.form.payload.classrooms.push(classroomPayload);
      });
    },
    createPlacePayload(payload) {
      if (!this.form.payload.place) {
        this.form.payload.place = {};
      }
      const placePayload = {
        org_id: this.activeOrg(),
        type: payload.type,
        name: payload.name,
        short_description: payload.short_description,
        long_description: payload.long_description,
        address: {
          type: "OTHER",
          title: payload.type ? payload.type.toLowerCase() : "",
          formatted_address: payload.formatted_address,
          address_1: payload.address_1,
          address_2: payload.address_2,
          floor: {
            type:
              this.form.payload.country_code === "IN" ? "GROUNDPLUS" : "GROUND",
            number: payload.floor,
          },
          landmark: payload.landmark,
          city: payload.city,
          state: {
            name: payload.state_name,
            code: payload.state_code,
          },
          country: {
            id: 0,
            name: payload.country_name,
            code: payload.country_code,
          },
          pin_zip: payload.postal_code,
          coordinates: {
            latitude: payload.google_map.coordinates.latitude,
            longitude: payload.google_map.coordinates.longitude,
          },
          google_maps: {
            place_id: payload.google_map.place_id,
            type: payload.google_map.types,
            colloquial_area: payload.google_map.colloquial_area,
            locality: payload.google_map.locality,
            sublocality: payload.google_map.sublocality,
            neighborhood: payload.google_map.neighborhood,
            postal_code: payload.google_map.postal_code,
            floor: "",
            landmark: "",
            administrative_area: payload.google_map.administrative_area,
          },
        },
        facilities: {
          car_parking: {
            self_parking: payload.self_parking,
            valet_service: payload.valet_parking,
          },
          bike_parking: payload.bike_parking,
        },
        open_hours: this.timeSlots.data.map((slot) => ({
          day: slot.day.substring(0, 3).toUpperCase(),
          is_available: !slot.isDayOff,
          time_slot:
            slot.availability.length > 0
              ? slot.availability.map((avail) => ({
                  start: this.convertTo24Hour(avail.startTime),
                  end: this.convertTo24Hour(avail.endTime),
                }))
              : [{ start: "00:00", end: "00:00" }],
        })),
      };

      this.form.payload.place = placePayload;
      console.log(this.form.payload.place, "placePayload");
    },

    // Helper method to convert 12-hour time format to 24-hour format
    convertTo24Hour(timeStr) {
      if (!timeStr) return "00:00";

      // Remove any extra spaces
      timeStr = timeStr.trim();

      // Check if the time is already in 24-hour format (no AM/PM)
      if (timeStr.includes(":") && !timeStr.match(/\s*(AM|PM)\s*/i)) {
        const [hours, minutes] = timeStr.split(":");
        const hour = parseInt(hours);

        // If it's already in 24-hour format, return as is
        if (hour >= 0 && hour <= 23) {
          return `${hours.padStart(2, "0")}:${minutes.padStart(2, "0")}`;
        }
      }

      // Extract hours, minutes, and AM/PM using a more flexible regex
      const match = timeStr.match(/(\d+):(\d+)\s*(AM|PM)/i);
      if (!match) {
        // Try alternative format without colon
        const altMatch = timeStr.match(/(\d+)(\d{2})\s*(AM|PM)/i);
        if (altMatch) {
          const [_, hours, minutes, period] = altMatch;
          return this.convertTimeTo24Hour(
            parseInt(hours),
            parseInt(minutes),
            period
          );
        }
        return "00:00";
      }

      const [_, hours, minutes, period] = match;
      return this.convertTimeTo24Hour(
        parseInt(hours),
        parseInt(minutes),
        period
      );
    },

    // Helper method to convert time components to 24-hour format
    convertTimeTo24Hour(hours, minutes, period) {
      // Convert to 24-hour format
      if (period.toUpperCase() === "PM" && hours < 12) {
        hours += 12;
      } else if (period.toUpperCase() === "AM" && hours === 12) {
        hours = 0;
      }

      return `${hours.toString().padStart(2, "0")}:${minutes
        .toString()
        .padStart(2, "0")}`;
    },
    classroomPosted(options) {
      console.log(options.response);
      if (options.response.status === 200) {
        console.log(options.response, "submittedclassroomform");

        // Get the current classroom index
        const currentIndex = options.index || 0;

        // If there are more classrooms to submit, submit the next one
        if (
          this.form.payload.classrooms &&
          currentIndex + 1 < this.form.payload.classrooms.length
        ) {
          this.submitClassroomPayload(currentIndex + 1);
        } else {
            this.form.payload.classrooms = [];
            this.form.payload.place = {};
            this.form.isLoading = false;
            this.showToastMessage(options.response.data.message);
			this.hideClassroomDetail();
        }
      }
    },
    submitClassroomPayload(index = 0) {
      // Get the classroom payload at the specified index
      let payload = this.form.payload.classrooms[index];

      const options = {
        apiURL: YUNOCommon.config.classroom("createClassroom", false),
        module: "gotData",
        store: "form",
        payload: payload,
        callback: true,
        callbackFunc: (options) => {
          // Add the index to the options so we know which classroom was submitted
          options.index = index;
          this.classroomPosted(options);
        },
      };

      this.$store.dispatch("postData", options);
    },
    formPosted(options) {
      if (options.response.status === 200) {
        console.log(options.response);
        let place_id = options.response.data.data.placeId;
        this.showToastMessage(options.response.data.message);
        // Create classroom payloads for all classrooms
        this.createClassroomPayload(this.form.payload, place_id);

        // Submit each classroom payload
        if (
          this.form.payload.classrooms &&
          this.form.payload.classrooms.length > 0
        ) {
          // Submit the first classroom
          this.submitClassroomPayload(0);
        }
      }
    },
    submitForm() {
      this.form.isLoading = true;
      this.createPlacePayload(this.form.payload);
      let payload = this.form.payload.place;
      const options = {
        apiURL: YUNOCommon.config.Places("create", false),
        module: "gotData",
        store: "form",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.formPosted(options),
      };
      this.$store.dispatch("postData", options);
    },
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },
  },
  watch: {
    "form.payload.car_parking"(newValue) {
      if (newValue === true) {
        this.form.payload.self_parking = true;
        this.form.payload.valet_parking = true;
      } else {
        this.form.payload.self_parking = false;
        this.form.payload.valet_parking = false;
      }
    },
  },
});
