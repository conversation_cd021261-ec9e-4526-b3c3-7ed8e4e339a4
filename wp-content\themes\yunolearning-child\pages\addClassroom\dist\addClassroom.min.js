/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function t(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function e(t,e,n,o){for(var a=-1,r=null==t?0:t.length;++a<r;){var i=t[a];e(o,i,n(i),t)}return o}function n(t,e){for(var n=-1,o=null==t?0:t.length;++n<o&&!1!==e(t[n],n,t););return t}function o(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}function a(t,e){for(var n=-1,o=null==t?0:t.length;++n<o;)if(!e(t[n],n,t))return!1;return!0}function r(t,e){for(var n=-1,o=null==t?0:t.length,a=0,r=[];++n<o;){var i=t[n];e(i,n,t)&&(r[a++]=i)}return r}function i(t,e){return!(null==t||!t.length)&&h(t,e,0)>-1}function s(t,e,n){for(var o=-1,a=null==t?0:t.length;++o<a;)if(n(e,t[o]))return!0;return!1}function l(t,e){for(var n=-1,o=null==t?0:t.length,a=Array(o);++n<o;)a[n]=e(t[n],n,t);return a}function u(t,e){for(var n=-1,o=e.length,a=t.length;++n<o;)t[a+n]=e[n];return t}function c(t,e,n,o){var a=-1,r=null==t?0:t.length;for(o&&r&&(n=t[++a]);++a<r;)n=e(n,t[a],a,t);return n}function d(t,e,n,o){var a=null==t?0:t.length;for(o&&a&&(n=t[--a]);a--;)n=e(n,t[a],a,t);return n}function p(t,e){for(var n=-1,o=null==t?0:t.length;++n<o;)if(e(t[n],n,t))return!0;return!1}function m(t){return t.match(Qt)||[]}function f(t,e,n){var o;return n(t,(function(t,n,a){if(e(t,n,a))return o=n,!1})),o}function g(t,e,n,o){for(var a=t.length,r=n+(o?1:-1);o?r--:++r<a;)if(e(t[r],r,t))return r;return-1}function h(t,e,n){return e==e?function(t,e,n){for(var o=n-1,a=t.length;++o<a;)if(t[o]===e)return o;return-1}(t,e,n):g(t,y,n)}function v(t,e,n,o){for(var a=n-1,r=t.length;++a<r;)if(o(t[a],e))return a;return-1}function y(t){return t!=t}function b(t,e){var n=null==t?0:t.length;return n?I(t,e)/n:X}function w(t){return function(e){return null==e?F:e[t]}}function _(t){return function(e){return null==t?F:t[e]}}function k(t,e,n,o,a){return a(t,(function(t,a,r){n=o?(o=!1,t):e(n,t,a,r)})),n}function I(t,e){for(var n,o=-1,a=t.length;++o<a;){var r=e(t[o]);r!==F&&(n=n===F?r:n+r)}return n}function L(t,e){for(var n=-1,o=Array(t);++n<t;)o[n]=e(n);return o}function S(t){return t?t.slice(0,N(t)+1).replace(Wt,""):t}function C(t){return function(e){return t(e)}}function A(t,e){return l(e,(function(e){return t[e]}))}function P(t,e){return t.has(e)}function j(t,e){for(var n=-1,o=t.length;++n<o&&h(e,t[n],0)>-1;);return n}function T(t,e){for(var n=t.length;n--&&h(e,t[n],0)>-1;);return n}function D(t){return"\\"+Ke[t]}function M(t){return Ye.test(t)}function x(t){return Ve.test(t)}function R(t){var e=-1,n=Array(t.size);return t.forEach((function(t,o){n[++e]=[o,t]})),n}function U(t,e){return function(n){return t(e(n))}}function H(t,e){for(var n=-1,o=t.length,a=0,r=[];++n<o;){var i=t[n];i!==e&&i!==V||(t[n]=V,r[a++]=n)}return r}function $(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}function O(t){return M(t)?function(t){for(var e=Fe.lastIndex=0;Fe.test(t);)++e;return e}(t):fn(t)}function E(t){return M(t)?function(t){return t.match(Fe)||[]}(t):function(t){return t.split("")}(t)}function N(t){for(var e=t.length;e--&&Gt.test(t.charAt(e)););return e}function B(t){return t.match(ze)||[]}var F,z="Expected a function",Y="__lodash_hash_undefined__",V="__lodash_placeholder__",W=16,G=32,q=64,J=128,K=256,Q=1/0,Z=9007199254740991,X=NaN,tt=4294967295,et=tt-1,nt=tt>>>1,ot=[["ary",J],["bind",1],["bindKey",2],["curry",8],["curryRight",W],["flip",512],["partial",G],["partialRight",q],["rearg",K]],at="[object Arguments]",rt="[object Array]",it="[object Boolean]",st="[object Date]",lt="[object Error]",ut="[object Function]",ct="[object GeneratorFunction]",dt="[object Map]",pt="[object Number]",mt="[object Object]",ft="[object Promise]",gt="[object RegExp]",ht="[object Set]",vt="[object String]",yt="[object Symbol]",bt="[object WeakMap]",wt="[object ArrayBuffer]",_t="[object DataView]",kt="[object Float32Array]",It="[object Float64Array]",Lt="[object Int8Array]",St="[object Int16Array]",Ct="[object Int32Array]",At="[object Uint8Array]",Pt="[object Uint8ClampedArray]",jt="[object Uint16Array]",Tt="[object Uint32Array]",Dt=/\b__p \+= '';/g,Mt=/\b(__p \+=) '' \+/g,xt=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Rt=/&(?:amp|lt|gt|quot|#39);/g,Ut=/[&<>"']/g,Ht=RegExp(Rt.source),$t=RegExp(Ut.source),Ot=/<%-([\s\S]+?)%>/g,Et=/<%([\s\S]+?)%>/g,Nt=/<%=([\s\S]+?)%>/g,Bt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ft=/^\w*$/,zt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Yt=/[\\^$.*+?()[\]{}|]/g,Vt=RegExp(Yt.source),Wt=/^\s+/,Gt=/\s/,qt=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Jt=/\{\n\/\* \[wrapped with (.+)\] \*/,Kt=/,? & /,Qt=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Zt=/[()=,{}\[\]\/\s]/,Xt=/\\(\\)?/g,te=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,ee=/\w*$/,ne=/^[-+]0x[0-9a-f]+$/i,oe=/^0b[01]+$/i,ae=/^\[object .+?Constructor\]$/,re=/^0o[0-7]+$/i,ie=/^(?:0|[1-9]\d*)$/,se=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,le=/($^)/,ue=/['\n\r\u2028\u2029\\]/g,ce="\\ud800-\\udfff",de="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pe="\\u2700-\\u27bf",me="a-z\\xdf-\\xf6\\xf8-\\xff",fe="A-Z\\xc0-\\xd6\\xd8-\\xde",ge="\\ufe0e\\ufe0f",he="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ve="['’]",ye="["+ce+"]",be="["+he+"]",we="["+de+"]",_e="\\d+",ke="["+pe+"]",Ie="["+me+"]",Le="[^"+ce+he+_e+pe+me+fe+"]",Se="\\ud83c[\\udffb-\\udfff]",Ce="[^"+ce+"]",Ae="(?:\\ud83c[\\udde6-\\uddff]){2}",Pe="[\\ud800-\\udbff][\\udc00-\\udfff]",je="["+fe+"]",Te="\\u200d",De="(?:"+Ie+"|"+Le+")",Me="(?:"+je+"|"+Le+")",xe="(?:['’](?:d|ll|m|re|s|t|ve))?",Re="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ue="(?:"+we+"|"+Se+")"+"?",He="["+ge+"]?",$e=He+Ue+("(?:"+Te+"(?:"+[Ce,Ae,Pe].join("|")+")"+He+Ue+")*"),Oe="(?:"+[ke,Ae,Pe].join("|")+")"+$e,Ee="(?:"+[Ce+we+"?",we,Ae,Pe,ye].join("|")+")",Ne=RegExp(ve,"g"),Be=RegExp(we,"g"),Fe=RegExp(Se+"(?="+Se+")|"+Ee+$e,"g"),ze=RegExp([je+"?"+Ie+"+"+xe+"(?="+[be,je,"$"].join("|")+")",Me+"+"+Re+"(?="+[be,je+De,"$"].join("|")+")",je+"?"+De+"+"+xe,je+"+"+Re,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_e,Oe].join("|"),"g"),Ye=RegExp("["+Te+ce+de+ge+"]"),Ve=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,We=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Ge=-1,qe={};qe[kt]=qe[It]=qe[Lt]=qe[St]=qe[Ct]=qe[At]=qe[Pt]=qe[jt]=qe[Tt]=!0,qe[at]=qe[rt]=qe[wt]=qe[it]=qe[_t]=qe[st]=qe[lt]=qe[ut]=qe[dt]=qe[pt]=qe[mt]=qe[gt]=qe[ht]=qe[vt]=qe[bt]=!1;var Je={};Je[at]=Je[rt]=Je[wt]=Je[_t]=Je[it]=Je[st]=Je[kt]=Je[It]=Je[Lt]=Je[St]=Je[Ct]=Je[dt]=Je[pt]=Je[mt]=Je[gt]=Je[ht]=Je[vt]=Je[yt]=Je[At]=Je[Pt]=Je[jt]=Je[Tt]=!0,Je[lt]=Je[ut]=Je[bt]=!1;var Ke={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qe=parseFloat,Ze=parseInt,Xe="object"==typeof global&&global&&global.Object===Object&&global,tn="object"==typeof self&&self&&self.Object===Object&&self,en=Xe||tn||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,on=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=on&&on.exports===nn,rn=an&&Xe.process,sn=function(){try{var t=on&&on.require&&on.require("util").types;return t||rn&&rn.binding&&rn.binding("util")}catch(t){}}(),ln=sn&&sn.isArrayBuffer,un=sn&&sn.isDate,cn=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,mn=sn&&sn.isTypedArray,fn=w("length"),gn=_({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),hn=_({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=_({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function _(Gt){function Qt(t){if(Ba(t)&&!Ts(t)&&!(t instanceof pe)){if(t instanceof de)return t;if(Tr.call(t,"__wrapped__"))return ma(t)}return new de(t)}function ce(){}function de(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=F}function pe(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=tt,this.__views__=[]}function me(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function fe(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function ge(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var o=t[e];this.set(o[0],o[1])}}function he(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new ge;++e<n;)this.add(t[e])}function ve(t){this.size=(this.__data__=new fe(t)).size}function ye(t,e){var n=Ts(t),o=!n&&js(t),a=!n&&!o&&Ms(t),r=!n&&!o&&!a&&$s(t),i=n||o||a||r,s=i?L(t.length,Ir):[],l=s.length;for(var u in t)!e&&!Tr.call(t,u)||i&&("length"==u||a&&("offset"==u||"parent"==u)||r&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Qo(u,l))||s.push(u);return s}function be(t){var e=t.length;return e?t[xn(0,e-1)]:F}function we(t,e){return ua(co(t),je(e,0,t.length))}function _e(t){return ua(co(t))}function ke(t,e,n){(n===F||xa(t[e],n))&&(n!==F||e in t)||Ae(t,e,n)}function Ie(t,e,n){var o=t[e];Tr.call(t,e)&&xa(o,n)&&(n!==F||e in t)||Ae(t,e,n)}function Le(t,e){for(var n=t.length;n--;)if(xa(t[n][0],e))return n;return-1}function Se(t,e,n,o){return Pi(t,(function(t,a,r){e(o,t,n(t),r)})),o}function Ce(t,e){return t&&po(e,er(e),t)}function Ae(t,e,n){"__proto__"==e&&qr?qr(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function Pe(t,e){for(var n=-1,o=e.length,a=hr(o),r=null==t;++n<o;)a[n]=r?F:Xa(t,e[n]);return a}function je(t,e,n){return t==t&&(n!==F&&(t=t<=n?t:n),e!==F&&(t=t>=e?t:e)),t}function Te(t,e,o,a,r,i){var s,l=1&e,u=2&e,c=4&e;if(o&&(s=r?o(t,a,r,i):o(t)),s!==F)return s;if(!Na(t))return t;var d=Ts(t);if(d){if(s=function(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&Tr.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(t),!l)return co(t,s)}else{var p=Ni(t),m=p==ut||p==ct;if(Ms(t))return ao(t,l);if(p==mt||p==at||m&&!r){if(s=u||m?{}:Jo(t),!l)return u?function(t,e){return po(t,Ei(t),e)}(t,function(t,e){return t&&po(e,nr(e),t)}(s,t)):function(t,e){return po(t,Oi(t),e)}(t,Ce(s,t))}else{if(!Je[p])return r?t:{};s=function(t,e,n){var o=t.constructor;switch(e){case wt:return ro(t);case it:case st:return new o(+t);case _t:return function(t,e){return new t.constructor(e?ro(t.buffer):t.buffer,t.byteOffset,t.byteLength)}(t,n);case kt:case It:case Lt:case St:case Ct:case At:case Pt:case jt:case Tt:return io(t,n);case dt:return new o;case pt:case vt:return new o(t);case gt:return function(t){var e=new t.constructor(t.source,ee.exec(t));return e.lastIndex=t.lastIndex,e}(t);case ht:return new o;case yt:return function(t){return Si?_r(Si.call(t)):{}}(t)}}(t,p,l)}}i||(i=new ve);var f=i.get(t);if(f)return f;i.set(t,s),Hs(t)?t.forEach((function(n){s.add(Te(n,e,o,n,t,i))})):Rs(t)&&t.forEach((function(n,a){s.set(a,Te(n,e,o,a,t,i))}));var g=d?F:(c?u?Bo:No:u?nr:er)(t);return n(g||t,(function(n,a){g&&(n=t[a=n]),Ie(s,a,Te(n,e,o,a,t,i))})),s}function De(t,e,n){var o=n.length;if(null==t)return!o;for(t=_r(t);o--;){var a=n[o],r=e[a],i=t[a];if(i===F&&!(a in t)||!r(i))return!1}return!0}function Me(t,e,n){if("function"!=typeof t)throw new Lr(z);return zi((function(){t.apply(F,n)}),e)}function xe(t,e,n,o){var a=-1,r=i,u=!0,c=t.length,d=[],p=e.length;if(!c)return d;n&&(e=l(e,C(n))),o?(r=s,u=!1):e.length>=200&&(r=P,u=!1,e=new he(e));t:for(;++a<c;){var m=t[a],f=null==n?m:n(m);if(m=o||0!==m?m:0,u&&f==f){for(var g=p;g--;)if(e[g]===f)continue t;d.push(m)}else r(e,f,o)||d.push(m)}return d}function Re(t,e){var n=!0;return Pi(t,(function(t,o,a){return n=!!e(t,o,a)})),n}function Ue(t,e,n){for(var o=-1,a=t.length;++o<a;){var r=t[o],i=e(r);if(null!=i&&(s===F?i==i&&!Va(i):n(i,s)))var s=i,l=r}return l}function He(t,e){var n=[];return Pi(t,(function(t,o,a){e(t,o,a)&&n.push(t)})),n}function $e(t,e,n,o,a){var r=-1,i=t.length;for(n||(n=Ko),a||(a=[]);++r<i;){var s=t[r];e>0&&n(s)?e>1?$e(s,e-1,n,o,a):u(a,s):o||(a[a.length]=s)}return a}function Oe(t,e){return t&&Ti(t,e,er)}function Ee(t,e){return t&&Di(t,e,er)}function Fe(t,e){return r(e,(function(e){return $a(t[e])}))}function ze(t,e){for(var n=0,o=(e=no(e,t)).length;null!=t&&n<o;)t=t[ca(e[n++])];return n&&n==o?t:F}function Ye(t,e,n){var o=e(t);return Ts(t)?o:u(o,n(t))}function Ve(t){return null==t?t===F?"[object Undefined]":"[object Null]":Gr&&Gr in _r(t)?function(t){var e=Tr.call(t,Gr),n=t[Gr];try{t[Gr]=F;var o=!0}catch(t){}var a=xr.call(t);return o&&(e?t[Gr]=n:delete t[Gr]),a}(t):function(t){return xr.call(t)}(t)}function Ke(t,e){return t>e}function Xe(t,e){return null!=t&&Tr.call(t,e)}function tn(t,e){return null!=t&&e in _r(t)}function nn(t,e,n){for(var o=n?s:i,a=t[0].length,r=t.length,u=r,c=hr(r),d=1/0,p=[];u--;){var m=t[u];u&&e&&(m=l(m,C(e))),d=ii(m.length,d),c[u]=!n&&(e||a>=120&&m.length>=120)?new he(u&&m):F}m=t[0];var f=-1,g=c[0];t:for(;++f<a&&p.length<d;){var h=m[f],v=e?e(h):h;if(h=n||0!==h?h:0,!(g?P(g,v):o(p,v,n))){for(u=r;--u;){var y=c[u];if(!(y?P(y,v):o(t[u],v,n)))continue t}g&&g.push(v),p.push(h)}}return p}function on(e,n,o){var a=null==(e=ra(e,n=no(n,e)))?e:e[ca(ya(n))];return null==a?F:t(a,e,o)}function rn(t){return Ba(t)&&Ve(t)==at}function sn(t,e,n,o,a){return t===e||(null==t||null==e||!Ba(t)&&!Ba(e)?t!=t&&e!=e:function(t,e,n,o,a,r){var i=Ts(t),s=Ts(e),l=i?rt:Ni(t),u=s?rt:Ni(e);l=l==at?mt:l,u=u==at?mt:u;var c=l==mt,d=u==mt,p=l==u;if(p&&Ms(t)){if(!Ms(e))return!1;i=!0,c=!1}if(p&&!c)return r||(r=new ve),i||$s(t)?Oo(t,e,n,o,a,r):function(t,e,n,o,a,r,i){switch(n){case _t:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case wt:return!(t.byteLength!=e.byteLength||!r(new Er(t),new Er(e)));case it:case st:case pt:return xa(+t,+e);case lt:return t.name==e.name&&t.message==e.message;case gt:case vt:return t==e+"";case dt:var s=R;case ht:var l=1&o;if(s||(s=$),t.size!=e.size&&!l)return!1;var u=i.get(t);if(u)return u==e;o|=2,i.set(t,e);var c=Oo(s(t),s(e),o,a,r,i);return i.delete(t),c;case yt:if(Si)return Si.call(t)==Si.call(e)}return!1}(t,e,l,n,o,a,r);if(!(1&n)){var m=c&&Tr.call(t,"__wrapped__"),f=d&&Tr.call(e,"__wrapped__");if(m||f){var g=m?t.value():t,h=f?e.value():e;return r||(r=new ve),a(g,h,n,o,r)}}return!!p&&(r||(r=new ve),function(t,e,n,o,a,r){var i=1&n,s=No(t),l=s.length;if(l!=No(e).length&&!i)return!1;for(var u=l;u--;){var c=s[u];if(!(i?c in e:Tr.call(e,c)))return!1}var d=r.get(t),p=r.get(e);if(d&&p)return d==e&&p==t;var m=!0;r.set(t,e),r.set(e,t);for(var f=i;++u<l;){var g=t[c=s[u]],h=e[c];if(o)var v=i?o(h,g,c,e,t,r):o(g,h,c,t,e,r);if(!(v===F?g===h||a(g,h,n,o,r):v)){m=!1;break}f||(f="constructor"==c)}if(m&&!f){var y=t.constructor,b=e.constructor;y!=b&&"constructor"in t&&"constructor"in e&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return r.delete(t),r.delete(e),m}(t,e,n,o,a,r))}(t,e,n,o,sn,a))}function fn(t,e,n,o){var a=n.length,r=a,i=!o;if(null==t)return!r;for(t=_r(t);a--;){var s=n[a];if(i&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++a<r;){var l=(s=n[a])[0],u=t[l],c=s[1];if(i&&s[2]){if(u===F&&!(l in t))return!1}else{var d=new ve;if(o)var p=o(u,c,l,t,e,d);if(!(p===F?sn(c,u,3,o,d):p))return!1}}return!0}function bn(t){return!(!Na(t)||function(t){return!!Mr&&Mr in t}(t))&&($a(t)?Hr:ae).test(da(t))}function wn(t){return"function"==typeof t?t:null==t?ur:"object"==typeof t?Ts(t)?Cn(t[0],t[1]):Sn(t):mr(t)}function _n(t){if(!ea(t))return ai(t);var e=[];for(var n in _r(t))Tr.call(t,n)&&"constructor"!=n&&e.push(n);return e}function kn(t){if(!Na(t))return function(t){var e=[];if(null!=t)for(var n in _r(t))e.push(n);return e}(t);var e=ea(t),n=[];for(var o in t)("constructor"!=o||!e&&Tr.call(t,o))&&n.push(o);return n}function In(t,e){return t<e}function Ln(t,e){var n=-1,o=Ra(t)?hr(t.length):[];return Pi(t,(function(t,a,r){o[++n]=e(t,a,r)})),o}function Sn(t){var e=Wo(t);return 1==e.length&&e[0][2]?oa(e[0][0],e[0][1]):function(n){return n===t||fn(n,t,e)}}function Cn(t,e){return Xo(t)&&na(e)?oa(ca(t),e):function(n){var o=Xa(n,t);return o===F&&o===e?tr(n,t):sn(e,o,3)}}function An(t,e,n,o,a){t!==e&&Ti(e,(function(r,i){if(a||(a=new ve),Na(r))!function(t,e,n,o,a,r,i){var s=ia(t,n),l=ia(e,n),u=i.get(l);if(u)return ke(t,n,u),F;var c=r?r(s,l,n+"",t,e,i):F,d=c===F;if(d){var p=Ts(l),m=!p&&Ms(l),f=!p&&!m&&$s(l);c=l,p||m||f?Ts(s)?c=s:Ua(s)?c=co(s):m?(d=!1,c=ao(l,!0)):f?(d=!1,c=io(l,!0)):c=[]:za(l)||js(l)?(c=s,js(s)?c=Qa(s):Na(s)&&!$a(s)||(c=Jo(l))):d=!1}d&&(i.set(l,c),a(c,l,o,r,i),i.delete(l)),ke(t,n,c)}(t,e,i,n,An,o,a);else{var s=o?o(ia(t,i),r,i+"",t,e,a):F;s===F&&(s=r),ke(t,i,s)}}),nr)}function Pn(t,e){var n=t.length;if(n)return Qo(e+=e<0?n:0,n)?t[e]:F}function jn(t,e,n){e=e.length?l(e,(function(t){return Ts(t)?function(e){return ze(e,1===t.length?t[0]:t)}:t})):[ur];var o=-1;return e=l(e,C(Yo())),function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}(Ln(t,(function(t,n,a){return{criteria:l(e,(function(e){return e(t)})),index:++o,value:t}})),(function(t,e){return function(t,e,n){for(var o=-1,a=t.criteria,r=e.criteria,i=a.length,s=n.length;++o<i;){var l=so(a[o],r[o]);if(l)return o>=s?l:l*("desc"==n[o]?-1:1)}return t.index-e.index}(t,e,n)}))}function Tn(t,e,n){for(var o=-1,a=e.length,r={};++o<a;){var i=e[o],s=ze(t,i);n(s,i)&&On(r,no(i,t),s)}return r}function Dn(t,e,n,o){var a=o?v:h,r=-1,i=e.length,s=t;for(t===e&&(e=co(e)),n&&(s=l(t,C(n)));++r<i;)for(var u=0,c=e[r],d=n?n(c):c;(u=a(s,d,u,o))>-1;)s!==t&&Yr.call(s,u,1),Yr.call(t,u,1);return t}function Mn(t,e){for(var n=t?e.length:0,o=n-1;n--;){var a=e[n];if(n==o||a!==r){var r=a;Qo(a)?Yr.call(t,a,1):qn(t,a)}}return t}function xn(t,e){return t+Xr(ui()*(e-t+1))}function Rn(t,e){var n="";if(!t||e<1||e>Z)return n;do{e%2&&(n+=t),(e=Xr(e/2))&&(t+=t)}while(e);return n}function Un(t,e){return Yi(aa(t,e,ur),t+"")}function Hn(t){return be(ar(t))}function $n(t,e){var n=ar(t);return ua(n,je(e,0,n.length))}function On(t,e,n,o){if(!Na(t))return t;for(var a=-1,r=(e=no(e,t)).length,i=r-1,s=t;null!=s&&++a<r;){var l=ca(e[a]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return t;if(a!=i){var c=s[l];(u=o?o(c,l,s):F)===F&&(u=Na(c)?c:Qo(e[a+1])?[]:{})}Ie(s,l,u),s=s[l]}return t}function En(t){return ua(ar(t))}function Nn(t,e,n){var o=-1,a=t.length;e<0&&(e=-e>a?0:a+e),(n=n>a?a:n)<0&&(n+=a),a=e>n?0:n-e>>>0,e>>>=0;for(var r=hr(a);++o<a;)r[o]=t[o+e];return r}function Bn(t,e){var n;return Pi(t,(function(t,o,a){return!(n=e(t,o,a))})),!!n}function Fn(t,e,n){var o=0,a=null==t?o:t.length;if("number"==typeof e&&e==e&&a<=nt){for(;o<a;){var r=o+a>>>1,i=t[r];null!==i&&!Va(i)&&(n?i<=e:i<e)?o=r+1:a=r}return a}return zn(t,e,ur,n)}function zn(t,e,n,o){var a=0,r=null==t?0:t.length;if(0===r)return 0;for(var i=(e=n(e))!=e,s=null===e,l=Va(e),u=e===F;a<r;){var c=Xr((a+r)/2),d=n(t[c]),p=d!==F,m=null===d,f=d==d,g=Va(d);if(i)var h=o||f;else h=u?f&&(o||p):s?f&&p&&(o||!m):l?f&&p&&!m&&(o||!g):!m&&!g&&(o?d<=e:d<e);h?a=c+1:r=c}return ii(r,et)}function Yn(t,e){for(var n=-1,o=t.length,a=0,r=[];++n<o;){var i=t[n],s=e?e(i):i;if(!n||!xa(s,l)){var l=s;r[a++]=0===i?0:i}}return r}function Vn(t){return"number"==typeof t?t:Va(t)?X:+t}function Wn(t){if("string"==typeof t)return t;if(Ts(t))return l(t,Wn)+"";if(Va(t))return Ci?Ci.call(t):"";var e=t+"";return"0"==e&&1/t==-Q?"-0":e}function Gn(t,e,n){var o=-1,a=i,r=t.length,l=!0,u=[],c=u;if(n)l=!1,a=s;else if(r>=200){var d=e?null:Hi(t);if(d)return $(d);l=!1,a=P,c=new he}else c=e?[]:u;t:for(;++o<r;){var p=t[o],m=e?e(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=c.length;f--;)if(c[f]===m)continue t;e&&c.push(m),u.push(p)}else a(c,m,n)||(c!==u&&c.push(m),u.push(p))}return u}function qn(t,e){return null==(t=ra(t,e=no(e,t)))||delete t[ca(ya(e))]}function Jn(t,e,n,o){return On(t,e,n(ze(t,e)),o)}function Kn(t,e,n,o){for(var a=t.length,r=o?a:-1;(o?r--:++r<a)&&e(t[r],r,t););return n?Nn(t,o?0:r,o?r+1:a):Nn(t,o?r+1:0,o?a:r)}function Qn(t,e){var n=t;return n instanceof pe&&(n=n.value()),c(e,(function(t,e){return e.func.apply(e.thisArg,u([t],e.args))}),n)}function Zn(t,e,n){var o=t.length;if(o<2)return o?Gn(t[0]):[];for(var a=-1,r=hr(o);++a<o;)for(var i=t[a],s=-1;++s<o;)s!=a&&(r[a]=xe(r[a]||i,t[s],e,n));return Gn($e(r,1),e,n)}function Xn(t,e,n){for(var o=-1,a=t.length,r=e.length,i={};++o<a;)n(i,t[o],o<r?e[o]:F);return i}function to(t){return Ua(t)?t:[]}function eo(t){return"function"==typeof t?t:ur}function no(t,e){return Ts(t)?t:Xo(t,e)?[t]:Vi(Za(t))}function oo(t,e,n){var o=t.length;return n=n===F?o:n,!e&&n>=o?t:Nn(t,e,n)}function ao(t,e){if(e)return t.slice();var n=t.length,o=Nr?Nr(n):new t.constructor(n);return t.copy(o),o}function ro(t){var e=new t.constructor(t.byteLength);return new Er(e).set(new Er(t)),e}function io(t,e){return new t.constructor(e?ro(t.buffer):t.buffer,t.byteOffset,t.length)}function so(t,e){if(t!==e){var n=t!==F,o=null===t,a=t==t,r=Va(t),i=e!==F,s=null===e,l=e==e,u=Va(e);if(!s&&!u&&!r&&t>e||r&&i&&l&&!s&&!u||o&&i&&l||!n&&l||!a)return 1;if(!o&&!r&&!u&&t<e||u&&n&&a&&!o&&!r||s&&n&&a||!i&&a||!l)return-1}return 0}function lo(t,e,n,o){for(var a=-1,r=t.length,i=n.length,s=-1,l=e.length,u=ri(r-i,0),c=hr(l+u),d=!o;++s<l;)c[s]=e[s];for(;++a<i;)(d||a<r)&&(c[n[a]]=t[a]);for(;u--;)c[s++]=t[a++];return c}function uo(t,e,n,o){for(var a=-1,r=t.length,i=-1,s=n.length,l=-1,u=e.length,c=ri(r-s,0),d=hr(c+u),p=!o;++a<c;)d[a]=t[a];for(var m=a;++l<u;)d[m+l]=e[l];for(;++i<s;)(p||a<r)&&(d[m+n[i]]=t[a++]);return d}function co(t,e){var n=-1,o=t.length;for(e||(e=hr(o));++n<o;)e[n]=t[n];return e}function po(t,e,n,o){var a=!n;n||(n={});for(var r=-1,i=e.length;++r<i;){var s=e[r],l=o?o(n[s],t[s],s,n,t):F;l===F&&(l=t[s]),a?Ae(n,s,l):Ie(n,s,l)}return n}function mo(t,n){return function(o,a){var r=Ts(o)?e:Se,i=n?n():{};return r(o,t,Yo(a,2),i)}}function fo(t){return Un((function(e,n){var o=-1,a=n.length,r=a>1?n[a-1]:F,i=a>2?n[2]:F;for(r=t.length>3&&"function"==typeof r?(a--,r):F,i&&Zo(n[0],n[1],i)&&(r=a<3?F:r,a=1),e=_r(e);++o<a;){var s=n[o];s&&t(e,s,o,r)}return e}))}function go(t,e){return function(n,o){if(null==n)return n;if(!Ra(n))return t(n,o);for(var a=n.length,r=e?a:-1,i=_r(n);(e?r--:++r<a)&&!1!==o(i[r],r,i););return n}}function ho(t){return function(e,n,o){for(var a=-1,r=_r(e),i=o(e),s=i.length;s--;){var l=i[t?s:++a];if(!1===n(r[l],l,r))break}return e}}function vo(t){return function(e){var n=M(e=Za(e))?E(e):F,o=n?n[0]:e.charAt(0),a=n?oo(n,1).join(""):e.slice(1);return o[t]()+a}}function yo(t){return function(e){return c(sr(ir(e).replace(Ne,"")),t,"")}}function bo(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=Ai(t.prototype),o=t.apply(n,e);return Na(o)?o:n}}function wo(e,n,o){var a=bo(e);return function r(){for(var i=arguments.length,s=hr(i),l=i,u=zo(r);l--;)s[l]=arguments[l];var c=i<3&&s[0]!==u&&s[i-1]!==u?[]:H(s,u);return(i-=c.length)<o?Do(e,n,Io,r.placeholder,F,s,c,F,F,o-i):t(this&&this!==en&&this instanceof r?a:e,this,s)}}function _o(t){return function(e,n,o){var a=_r(e);if(!Ra(e)){var r=Yo(n,3);e=er(e),n=function(t){return r(a[t],t,a)}}var i=t(e,n,o);return i>-1?a[r?e[i]:i]:F}}function ko(t){return Eo((function(e){var n=e.length,o=n,a=de.prototype.thru;for(t&&e.reverse();o--;){var r=e[o];if("function"!=typeof r)throw new Lr(z);if(a&&!i&&"wrapper"==Fo(r))var i=new de([],!0)}for(o=i?o:n;++o<n;){var s=Fo(r=e[o]),l="wrapper"==s?$i(r):F;i=l&&ta(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?i[Fo(l[0])].apply(i,l[3]):1==r.length&&ta(r)?i[s]():i.thru(r)}return function(){var t=arguments,o=t[0];if(i&&1==t.length&&Ts(o))return i.plant(o).value();for(var a=0,r=n?e[a].apply(this,t):o;++a<n;)r=e[a].call(this,r);return r}}))}function Io(t,e,n,o,a,r,i,s,l,u){var c=e&J,d=1&e,p=2&e,m=24&e,f=512&e,g=p?F:bo(t);return function h(){for(var v=arguments.length,y=hr(v),b=v;b--;)y[b]=arguments[b];if(m)var w=zo(h),_=function(t,e){for(var n=t.length,o=0;n--;)t[n]===e&&++o;return o}(y,w);if(o&&(y=lo(y,o,a,m)),r&&(y=uo(y,r,i,m)),v-=_,m&&v<u)return Do(t,e,Io,h.placeholder,n,y,H(y,w),s,l,u-v);var k=d?n:this,I=p?k[t]:t;return v=y.length,s?y=function(t,e){for(var n=t.length,o=ii(e.length,n),a=co(t);o--;){var r=e[o];t[o]=Qo(r,n)?a[r]:F}return t}(y,s):f&&v>1&&y.reverse(),c&&l<v&&(y.length=l),this&&this!==en&&this instanceof h&&(I=g||bo(I)),I.apply(k,y)}}function Lo(t,e){return function(n,o){return function(t,e,n,o){return Oe(t,(function(t,a,r){e(o,n(t),a,r)})),o}(n,t,e(o),{})}}function So(t,e){return function(n,o){var a;if(n===F&&o===F)return e;if(n!==F&&(a=n),o!==F){if(a===F)return o;"string"==typeof n||"string"==typeof o?(n=Wn(n),o=Wn(o)):(n=Vn(n),o=Vn(o)),a=t(n,o)}return a}}function Co(e){return Eo((function(n){return n=l(n,C(Yo())),Un((function(o){var a=this;return e(n,(function(e){return t(e,a,o)}))}))}))}function Ao(t,e){var n=(e=e===F?" ":Wn(e)).length;if(n<2)return n?Rn(e,t):e;var o=Rn(e,Zr(t/O(e)));return M(e)?oo(E(o),0,t).join(""):o.slice(0,t)}function Po(e,n,o,a){var r=1&n,i=bo(e);return function n(){for(var s=-1,l=arguments.length,u=-1,c=a.length,d=hr(c+l),p=this&&this!==en&&this instanceof n?i:e;++u<c;)d[u]=a[u];for(;l--;)d[u++]=arguments[++s];return t(p,r?o:this,d)}}function jo(t){return function(e,n,o){return o&&"number"!=typeof o&&Zo(e,n,o)&&(n=o=F),e=Ga(e),n===F?(n=e,e=0):n=Ga(n),function(t,e,n,o){for(var a=-1,r=ri(Zr((e-t)/(n||1)),0),i=hr(r);r--;)i[o?r:++a]=t,t+=n;return i}(e,n,o=o===F?e<n?1:-1:Ga(o),t)}}function To(t){return function(e,n){return"string"==typeof e&&"string"==typeof n||(e=Ka(e),n=Ka(n)),t(e,n)}}function Do(t,e,n,o,a,r,i,s,l,u){var c=8&e;e|=c?G:q,4&(e&=~(c?q:G))||(e&=-4);var d=[t,e,a,c?r:F,c?i:F,c?F:r,c?F:i,s,l,u],p=n.apply(F,d);return ta(t)&&Fi(p,d),p.placeholder=o,sa(p,t,e)}function Mo(t){var e=wr[t];return function(t,n){if(t=Ka(t),(n=null==n?0:ii(qa(n),292))&&ni(t)){var o=(Za(t)+"e").split("e");return+((o=(Za(e(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return e(t)}}function xo(t){return function(e){var n=Ni(e);return n==dt?R(e):n==ht?function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=[t,t]})),n}(e):function(t,e){return l(e,(function(e){return[e,t[e]]}))}(e,t(e))}}function Ro(t,e,n,o,a,r,i,s){var l=2&e;if(!l&&"function"!=typeof t)throw new Lr(z);var u=o?o.length:0;if(u||(e&=-97,o=a=F),i=i===F?i:ri(qa(i),0),s=s===F?s:qa(s),u-=a?a.length:0,e&q){var c=o,d=a;o=a=F}var p=l?F:$i(t),m=[t,e,n,o,a,c,d,r,i,s];if(p&&function(t,e){var n=t[1],o=e[1],a=n|o,r=a<131,i=o==J&&8==n||o==J&&n==K&&t[7].length<=e[8]||384==o&&e[7].length<=e[8]&&8==n;if(!r&&!i)return t;1&o&&(t[2]=e[2],a|=1&n?0:4);var s=e[3];if(s){var l=t[3];t[3]=l?lo(l,s,e[4]):s,t[4]=l?H(t[3],V):e[4]}s=e[5],s&&(l=t[5],t[5]=l?uo(l,s,e[6]):s,t[6]=l?H(t[5],V):e[6]),s=e[7],s&&(t[7]=s),o&J&&(t[8]=null==t[8]?e[8]:ii(t[8],e[8])),null==t[9]&&(t[9]=e[9]),t[0]=e[0],t[1]=a}(m,p),t=m[0],e=m[1],n=m[2],o=m[3],a=m[4],!(s=m[9]=m[9]===F?l?0:t.length:ri(m[9]-u,0))&&24&e&&(e&=-25),e&&1!=e)f=8==e||e==W?wo(t,e,s):e!=G&&33!=e||a.length?Io.apply(F,m):Po(t,e,n,o);else var f=function(t,e,n){var o=1&e,a=bo(t);return function e(){return(this&&this!==en&&this instanceof e?a:t).apply(o?n:this,arguments)}}(t,e,n);return sa((p?Mi:Fi)(f,m),t,e)}function Uo(t,e,n,o){return t===F||xa(t,Ar[n])&&!Tr.call(o,n)?e:t}function Ho(t,e,n,o,a,r){return Na(t)&&Na(e)&&(r.set(e,t),An(t,e,F,Ho,r),r.delete(e)),t}function $o(t){return za(t)?F:t}function Oo(t,e,n,o,a,r){var i=1&n,s=t.length,l=e.length;if(s!=l&&!(i&&l>s))return!1;var u=r.get(t),c=r.get(e);if(u&&c)return u==e&&c==t;var d=-1,m=!0,f=2&n?new he:F;for(r.set(t,e),r.set(e,t);++d<s;){var g=t[d],h=e[d];if(o)var v=i?o(h,g,d,e,t,r):o(g,h,d,t,e,r);if(v!==F){if(v)continue;m=!1;break}if(f){if(!p(e,(function(t,e){if(!P(f,e)&&(g===t||a(g,t,n,o,r)))return f.push(e)}))){m=!1;break}}else if(g!==h&&!a(g,h,n,o,r)){m=!1;break}}return r.delete(t),r.delete(e),m}function Eo(t){return Yi(aa(t,F,ha),t+"")}function No(t){return Ye(t,er,Oi)}function Bo(t){return Ye(t,nr,Ei)}function Fo(t){for(var e=t.name+"",n=yi[e],o=Tr.call(yi,e)?n.length:0;o--;){var a=n[o],r=a.func;if(null==r||r==t)return a.name}return e}function zo(t){return(Tr.call(Qt,"placeholder")?Qt:t).placeholder}function Yo(){var t=Qt.iteratee||cr;return t=t===cr?wn:t,arguments.length?t(arguments[0],arguments[1]):t}function Vo(t,e){var n=t.__data__;return function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}(e)?n["string"==typeof e?"string":"hash"]:n.map}function Wo(t){for(var e=er(t),n=e.length;n--;){var o=e[n],a=t[o];e[n]=[o,a,na(a)]}return e}function Go(t,e){var n=function(t,e){return null==t?F:t[e]}(t,e);return bn(n)?n:F}function qo(t,e,n){for(var o=-1,a=(e=no(e,t)).length,r=!1;++o<a;){var i=ca(e[o]);if(!(r=null!=t&&n(t,i)))break;t=t[i]}return r||++o!=a?r:!!(a=null==t?0:t.length)&&Ea(a)&&Qo(i,a)&&(Ts(t)||js(t))}function Jo(t){return"function"!=typeof t.constructor||ea(t)?{}:Ai(Br(t))}function Ko(t){return Ts(t)||js(t)||!!(Vr&&t&&t[Vr])}function Qo(t,e){var n=typeof t;return!!(e=null==e?Z:e)&&("number"==n||"symbol"!=n&&ie.test(t))&&t>-1&&t%1==0&&t<e}function Zo(t,e,n){if(!Na(n))return!1;var o=typeof e;return!!("number"==o?Ra(n)&&Qo(e,n.length):"string"==o&&e in n)&&xa(n[e],t)}function Xo(t,e){if(Ts(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!Va(t))||Ft.test(t)||!Bt.test(t)||null!=e&&t in _r(e)}function ta(t){var e=Fo(t),n=Qt[e];if("function"!=typeof n||!(e in pe.prototype))return!1;if(t===n)return!0;var o=$i(n);return!!o&&t===o[0]}function ea(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Ar)}function na(t){return t==t&&!Na(t)}function oa(t,e){return function(n){return null!=n&&n[t]===e&&(e!==F||t in _r(n))}}function aa(e,n,o){return n=ri(n===F?e.length-1:n,0),function(){for(var a=arguments,r=-1,i=ri(a.length-n,0),s=hr(i);++r<i;)s[r]=a[n+r];r=-1;for(var l=hr(n+1);++r<n;)l[r]=a[r];return l[n]=o(s),t(e,this,l)}}function ra(t,e){return e.length<2?t:ze(t,Nn(e,0,-1))}function ia(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}function sa(t,e,n){var o=e+"";return Yi(t,function(t,e){var n=e.length;if(!n)return t;var o=n-1;return e[o]=(n>1?"& ":"")+e[o],e=e.join(n>2?", ":" "),t.replace(qt,"{\n/* [wrapped with "+e+"] */\n")}(o,pa(function(t){var e=t.match(Jt);return e?e[1].split(Kt):[]}(o),n)))}function la(t){var e=0,n=0;return function(){var o=si(),a=16-(o-n);if(n=o,a>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(F,arguments)}}function ua(t,e){var n=-1,o=t.length,a=o-1;for(e=e===F?o:e;++n<e;){var r=xn(n,a),i=t[r];t[r]=t[n],t[n]=i}return t.length=e,t}function ca(t){if("string"==typeof t||Va(t))return t;var e=t+"";return"0"==e&&1/t==-Q?"-0":e}function da(t){if(null!=t){try{return jr.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function pa(t,e){return n(ot,(function(n){var o="_."+n[0];e&n[1]&&!i(t,o)&&t.push(o)})),t.sort()}function ma(t){if(t instanceof pe)return t.clone();var e=new de(t.__wrapped__,t.__chain__);return e.__actions__=co(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}function fa(t,e,n){var o=null==t?0:t.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=ri(o+a,0)),g(t,Yo(e,3),a)}function ga(t,e,n){var o=null==t?0:t.length;if(!o)return-1;var a=o-1;return n!==F&&(a=qa(n),a=n<0?ri(o+a,0):ii(a,o-1)),g(t,Yo(e,3),a,!0)}function ha(t){return null!=t&&t.length?$e(t,1):[]}function va(t){return t&&t.length?t[0]:F}function ya(t){var e=null==t?0:t.length;return e?t[e-1]:F}function ba(t,e){return t&&t.length&&e&&e.length?Dn(t,e):t}function wa(t){return null==t?t:ci.call(t)}function _a(t){if(!t||!t.length)return[];var e=0;return t=r(t,(function(t){if(Ua(t))return e=ri(t.length,e),!0})),L(e,(function(e){return l(t,w(e))}))}function ka(e,n){if(!e||!e.length)return[];var o=_a(e);return null==n?o:l(o,(function(e){return t(n,F,e)}))}function Ia(t){var e=Qt(t);return e.__chain__=!0,e}function La(t,e){return e(t)}function Sa(t,e){return(Ts(t)?n:Pi)(t,Yo(e,3))}function Ca(t,e){return(Ts(t)?o:ji)(t,Yo(e,3))}function Aa(t,e){return(Ts(t)?l:Ln)(t,Yo(e,3))}function Pa(t,e,n){return e=n?F:e,e=t&&null==e?t.length:e,Ro(t,J,F,F,F,F,e)}function ja(t,e){var n;if("function"!=typeof e)throw new Lr(z);return t=qa(t),function(){return--t>0&&(n=e.apply(this,arguments)),t<=1&&(e=F),n}}function Ta(t,e,n){function o(e){var n=l,o=u;return l=u=F,f=e,d=t.apply(o,n)}function a(t){var n=t-m;return m===F||n>=e||n<0||h&&t-f>=c}function r(){var t=ys();return a(t)?i(t):(p=zi(r,function(t){var n=e-(t-m);return h?ii(n,c-(t-f)):n}(t)),F)}function i(t){return p=F,v&&l?o(t):(l=u=F,d)}function s(){var t=ys(),n=a(t);if(l=arguments,u=this,m=t,n){if(p===F)return function(t){return f=t,p=zi(r,e),g?o(t):d}(m);if(h)return Ui(p),p=zi(r,e),o(m)}return p===F&&(p=zi(r,e)),d}var l,u,c,d,p,m,f=0,g=!1,h=!1,v=!0;if("function"!=typeof t)throw new Lr(z);return e=Ka(e)||0,Na(n)&&(g=!!n.leading,c=(h="maxWait"in n)?ri(Ka(n.maxWait)||0,e):c,v="trailing"in n?!!n.trailing:v),s.cancel=function(){p!==F&&Ui(p),f=0,l=m=u=p=F},s.flush=function(){return p===F?d:i(ys())},s}function Da(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new Lr(z);var n=function(){var o=arguments,a=e?e.apply(this,o):o[0],r=n.cache;if(r.has(a))return r.get(a);var i=t.apply(this,o);return n.cache=r.set(a,i)||r,i};return n.cache=new(Da.Cache||ge),n}function Ma(t){if("function"!=typeof t)throw new Lr(z);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}function xa(t,e){return t===e||t!=t&&e!=e}function Ra(t){return null!=t&&Ea(t.length)&&!$a(t)}function Ua(t){return Ba(t)&&Ra(t)}function Ha(t){if(!Ba(t))return!1;var e=Ve(t);return e==lt||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!za(t)}function $a(t){if(!Na(t))return!1;var e=Ve(t);return e==ut||e==ct||"[object AsyncFunction]"==e||"[object Proxy]"==e}function Oa(t){return"number"==typeof t&&t==qa(t)}function Ea(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=Z}function Na(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Ba(t){return null!=t&&"object"==typeof t}function Fa(t){return"number"==typeof t||Ba(t)&&Ve(t)==pt}function za(t){if(!Ba(t)||Ve(t)!=mt)return!1;var e=Br(t);if(null===e)return!0;var n=Tr.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&jr.call(n)==Rr}function Ya(t){return"string"==typeof t||!Ts(t)&&Ba(t)&&Ve(t)==vt}function Va(t){return"symbol"==typeof t||Ba(t)&&Ve(t)==yt}function Wa(t){if(!t)return[];if(Ra(t))return Ya(t)?E(t):co(t);if(Wr&&t[Wr])return function(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}(t[Wr]());var e=Ni(t);return(e==dt?R:e==ht?$:ar)(t)}function Ga(t){return t?(t=Ka(t))===Q||t===-Q?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function qa(t){var e=Ga(t),n=e%1;return e==e?n?e-n:e:0}function Ja(t){return t?je(qa(t),0,tt):0}function Ka(t){if("number"==typeof t)return t;if(Va(t))return X;if(Na(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Na(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=S(t);var n=oe.test(t);return n||re.test(t)?Ze(t.slice(2),n?2:8):ne.test(t)?X:+t}function Qa(t){return po(t,nr(t))}function Za(t){return null==t?"":Wn(t)}function Xa(t,e,n){var o=null==t?F:ze(t,e);return o===F?n:o}function tr(t,e){return null!=t&&qo(t,e,tn)}function er(t){return Ra(t)?ye(t):_n(t)}function nr(t){return Ra(t)?ye(t,!0):kn(t)}function or(t,e){if(null==t)return{};var n=l(Bo(t),(function(t){return[t]}));return e=Yo(e),Tn(t,n,(function(t,n){return e(t,n[0])}))}function ar(t){return null==t?[]:A(t,er(t))}function rr(t){return ul(Za(t).toLowerCase())}function ir(t){return(t=Za(t))&&t.replace(se,gn).replace(Be,"")}function sr(t,e,n){return t=Za(t),(e=n?F:e)===F?x(t)?B(t):m(t):t.match(e)||[]}function lr(t){return function(){return t}}function ur(t){return t}function cr(t){return wn("function"==typeof t?t:Te(t,1))}function dr(t,e,o){var a=er(e),r=Fe(e,a);null!=o||Na(e)&&(r.length||!a.length)||(o=e,e=t,t=this,r=Fe(e,er(e)));var i=!(Na(o)&&"chain"in o&&!o.chain),s=$a(t);return n(r,(function(n){var o=e[n];t[n]=o,s&&(t.prototype[n]=function(){var e=this.__chain__;if(i||e){var n=t(this.__wrapped__);return(n.__actions__=co(this.__actions__)).push({func:o,args:arguments,thisArg:t}),n.__chain__=e,n}return o.apply(t,u([this.value()],arguments))})})),t}function pr(){}function mr(t){return Xo(t)?w(ca(t)):function(t){return function(e){return ze(e,t)}}(t)}function fr(){return[]}function gr(){return!1}var hr=(Gt=null==Gt?en:yn.defaults(en.Object(),Gt,yn.pick(en,We))).Array,vr=Gt.Date,yr=Gt.Error,br=Gt.Function,wr=Gt.Math,_r=Gt.Object,kr=Gt.RegExp,Ir=Gt.String,Lr=Gt.TypeError,Sr=hr.prototype,Cr=br.prototype,Ar=_r.prototype,Pr=Gt["__core-js_shared__"],jr=Cr.toString,Tr=Ar.hasOwnProperty,Dr=0,Mr=function(){var t=/[^.]+$/.exec(Pr&&Pr.keys&&Pr.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),xr=Ar.toString,Rr=jr.call(_r),Ur=en._,Hr=kr("^"+jr.call(Tr).replace(Yt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),$r=an?Gt.Buffer:F,Or=Gt.Symbol,Er=Gt.Uint8Array,Nr=$r?$r.allocUnsafe:F,Br=U(_r.getPrototypeOf,_r),Fr=_r.create,zr=Ar.propertyIsEnumerable,Yr=Sr.splice,Vr=Or?Or.isConcatSpreadable:F,Wr=Or?Or.iterator:F,Gr=Or?Or.toStringTag:F,qr=function(){try{var t=Go(_r,"defineProperty");return t({},"",{}),t}catch(t){}}(),Jr=Gt.clearTimeout!==en.clearTimeout&&Gt.clearTimeout,Kr=vr&&vr.now!==en.Date.now&&vr.now,Qr=Gt.setTimeout!==en.setTimeout&&Gt.setTimeout,Zr=wr.ceil,Xr=wr.floor,ti=_r.getOwnPropertySymbols,ei=$r?$r.isBuffer:F,ni=Gt.isFinite,oi=Sr.join,ai=U(_r.keys,_r),ri=wr.max,ii=wr.min,si=vr.now,li=Gt.parseInt,ui=wr.random,ci=Sr.reverse,di=Go(Gt,"DataView"),pi=Go(Gt,"Map"),mi=Go(Gt,"Promise"),fi=Go(Gt,"Set"),gi=Go(Gt,"WeakMap"),hi=Go(_r,"create"),vi=gi&&new gi,yi={},bi=da(di),wi=da(pi),_i=da(mi),ki=da(fi),Ii=da(gi),Li=Or?Or.prototype:F,Si=Li?Li.valueOf:F,Ci=Li?Li.toString:F,Ai=function(){function t(){}return function(e){if(!Na(e))return{};if(Fr)return Fr(e);t.prototype=e;var n=new t;return t.prototype=F,n}}();Qt.templateSettings={escape:Ot,evaluate:Et,interpolate:Nt,variable:"",imports:{_:Qt}},Qt.prototype=ce.prototype,Qt.prototype.constructor=Qt,de.prototype=Ai(ce.prototype),de.prototype.constructor=de,pe.prototype=Ai(ce.prototype),pe.prototype.constructor=pe,me.prototype.clear=function(){this.__data__=hi?hi(null):{},this.size=0},me.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},me.prototype.get=function(t){var e=this.__data__;if(hi){var n=e[t];return n===Y?F:n}return Tr.call(e,t)?e[t]:F},me.prototype.has=function(t){var e=this.__data__;return hi?e[t]!==F:Tr.call(e,t)},me.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=hi&&e===F?Y:e,this},fe.prototype.clear=function(){this.__data__=[],this.size=0},fe.prototype.delete=function(t){var e=this.__data__,n=Le(e,t);return!(n<0||(n==e.length-1?e.pop():Yr.call(e,n,1),--this.size,0))},fe.prototype.get=function(t){var e=this.__data__,n=Le(e,t);return n<0?F:e[n][1]},fe.prototype.has=function(t){return Le(this.__data__,t)>-1},fe.prototype.set=function(t,e){var n=this.__data__,o=Le(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this},ge.prototype.clear=function(){this.size=0,this.__data__={hash:new me,map:new(pi||fe),string:new me}},ge.prototype.delete=function(t){var e=Vo(this,t).delete(t);return this.size-=e?1:0,e},ge.prototype.get=function(t){return Vo(this,t).get(t)},ge.prototype.has=function(t){return Vo(this,t).has(t)},ge.prototype.set=function(t,e){var n=Vo(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this},he.prototype.add=he.prototype.push=function(t){return this.__data__.set(t,Y),this},he.prototype.has=function(t){return this.__data__.has(t)},ve.prototype.clear=function(){this.__data__=new fe,this.size=0},ve.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},ve.prototype.get=function(t){return this.__data__.get(t)},ve.prototype.has=function(t){return this.__data__.has(t)},ve.prototype.set=function(t,e){var n=this.__data__;if(n instanceof fe){var o=n.__data__;if(!pi||o.length<199)return o.push([t,e]),this.size=++n.size,this;n=this.__data__=new ge(o)}return n.set(t,e),this.size=n.size,this};var Pi=go(Oe),ji=go(Ee,!0),Ti=ho(),Di=ho(!0),Mi=vi?function(t,e){return vi.set(t,e),t}:ur,xi=qr?function(t,e){return qr(t,"toString",{configurable:!0,enumerable:!1,value:lr(e),writable:!0})}:ur,Ri=Un,Ui=Jr||function(t){return en.clearTimeout(t)},Hi=fi&&1/$(new fi([,-0]))[1]==Q?function(t){return new fi(t)}:pr,$i=vi?function(t){return vi.get(t)}:pr,Oi=ti?function(t){return null==t?[]:(t=_r(t),r(ti(t),(function(e){return zr.call(t,e)})))}:fr,Ei=ti?function(t){for(var e=[];t;)u(e,Oi(t)),t=Br(t);return e}:fr,Ni=Ve;(di&&Ni(new di(new ArrayBuffer(1)))!=_t||pi&&Ni(new pi)!=dt||mi&&Ni(mi.resolve())!=ft||fi&&Ni(new fi)!=ht||gi&&Ni(new gi)!=bt)&&(Ni=function(t){var e=Ve(t),n=e==mt?t.constructor:F,o=n?da(n):"";if(o)switch(o){case bi:return _t;case wi:return dt;case _i:return ft;case ki:return ht;case Ii:return bt}return e});var Bi=Pr?$a:gr,Fi=la(Mi),zi=Qr||function(t,e){return en.setTimeout(t,e)},Yi=la(xi),Vi=function(t){var e=Da(t,(function(t){return 500===n.size&&n.clear(),t})),n=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(zt,(function(t,n,o,a){e.push(o?a.replace(Xt,"$1"):n||t)})),e})),Wi=Un((function(t,e){return Ua(t)?xe(t,$e(e,1,Ua,!0)):[]})),Gi=Un((function(t,e){var n=ya(e);return Ua(n)&&(n=F),Ua(t)?xe(t,$e(e,1,Ua,!0),Yo(n,2)):[]})),qi=Un((function(t,e){var n=ya(e);return Ua(n)&&(n=F),Ua(t)?xe(t,$e(e,1,Ua,!0),F,n):[]})),Ji=Un((function(t){var e=l(t,to);return e.length&&e[0]===t[0]?nn(e):[]})),Ki=Un((function(t){var e=ya(t),n=l(t,to);return e===ya(n)?e=F:n.pop(),n.length&&n[0]===t[0]?nn(n,Yo(e,2)):[]})),Qi=Un((function(t){var e=ya(t),n=l(t,to);return(e="function"==typeof e?e:F)&&n.pop(),n.length&&n[0]===t[0]?nn(n,F,e):[]})),Zi=Un(ba),Xi=Eo((function(t,e){var n=null==t?0:t.length,o=Pe(t,e);return Mn(t,l(e,(function(t){return Qo(t,n)?+t:t})).sort(so)),o})),ts=Un((function(t){return Gn($e(t,1,Ua,!0))})),es=Un((function(t){var e=ya(t);return Ua(e)&&(e=F),Gn($e(t,1,Ua,!0),Yo(e,2))})),ns=Un((function(t){var e=ya(t);return e="function"==typeof e?e:F,Gn($e(t,1,Ua,!0),F,e)})),os=Un((function(t,e){return Ua(t)?xe(t,e):[]})),as=Un((function(t){return Zn(r(t,Ua))})),rs=Un((function(t){var e=ya(t);return Ua(e)&&(e=F),Zn(r(t,Ua),Yo(e,2))})),is=Un((function(t){var e=ya(t);return e="function"==typeof e?e:F,Zn(r(t,Ua),F,e)})),ss=Un(_a),ls=Un((function(t){var e=t.length,n=e>1?t[e-1]:F;return n="function"==typeof n?(t.pop(),n):F,ka(t,n)})),us=Eo((function(t){var e=t.length,n=e?t[0]:0,o=this.__wrapped__,a=function(e){return Pe(e,t)};return!(e>1||this.__actions__.length)&&o instanceof pe&&Qo(n)?((o=o.slice(n,+n+(e?1:0))).__actions__.push({func:La,args:[a],thisArg:F}),new de(o,this.__chain__).thru((function(t){return e&&!t.length&&t.push(F),t}))):this.thru(a)})),cs=mo((function(t,e,n){Tr.call(t,n)?++t[n]:Ae(t,n,1)})),ds=_o(fa),ps=_o(ga),ms=mo((function(t,e,n){Tr.call(t,n)?t[n].push(e):Ae(t,n,[e])})),fs=Un((function(e,n,o){var a=-1,r="function"==typeof n,i=Ra(e)?hr(e.length):[];return Pi(e,(function(e){i[++a]=r?t(n,e,o):on(e,n,o)})),i})),gs=mo((function(t,e,n){Ae(t,n,e)})),hs=mo((function(t,e,n){t[n?0:1].push(e)}),(function(){return[[],[]]})),vs=Un((function(t,e){if(null==t)return[];var n=e.length;return n>1&&Zo(t,e[0],e[1])?e=[]:n>2&&Zo(e[0],e[1],e[2])&&(e=[e[0]]),jn(t,$e(e,1),[])})),ys=Kr||function(){return en.Date.now()},bs=Un((function(t,e,n){var o=1;if(n.length){var a=H(n,zo(bs));o|=G}return Ro(t,o,e,n,a)})),ws=Un((function(t,e,n){var o=3;if(n.length){var a=H(n,zo(ws));o|=G}return Ro(e,o,t,n,a)})),_s=Un((function(t,e){return Me(t,1,e)})),ks=Un((function(t,e,n){return Me(t,Ka(e)||0,n)}));Da.Cache=ge;var Is=Ri((function(e,n){var o=(n=1==n.length&&Ts(n[0])?l(n[0],C(Yo())):l($e(n,1),C(Yo()))).length;return Un((function(a){for(var r=-1,i=ii(a.length,o);++r<i;)a[r]=n[r].call(this,a[r]);return t(e,this,a)}))})),Ls=Un((function(t,e){return Ro(t,G,F,e,H(e,zo(Ls)))})),Ss=Un((function(t,e){return Ro(t,q,F,e,H(e,zo(Ss)))})),Cs=Eo((function(t,e){return Ro(t,K,F,F,F,e)})),As=To(Ke),Ps=To((function(t,e){return t>=e})),js=rn(function(){return arguments}())?rn:function(t){return Ba(t)&&Tr.call(t,"callee")&&!zr.call(t,"callee")},Ts=hr.isArray,Ds=ln?C(ln):function(t){return Ba(t)&&Ve(t)==wt},Ms=ei||gr,xs=un?C(un):function(t){return Ba(t)&&Ve(t)==st},Rs=cn?C(cn):function(t){return Ba(t)&&Ni(t)==dt},Us=dn?C(dn):function(t){return Ba(t)&&Ve(t)==gt},Hs=pn?C(pn):function(t){return Ba(t)&&Ni(t)==ht},$s=mn?C(mn):function(t){return Ba(t)&&Ea(t.length)&&!!qe[Ve(t)]},Os=To(In),Es=To((function(t,e){return t<=e})),Ns=fo((function(t,e){if(ea(e)||Ra(e))return po(e,er(e),t),F;for(var n in e)Tr.call(e,n)&&Ie(t,n,e[n])})),Bs=fo((function(t,e){po(e,nr(e),t)})),Fs=fo((function(t,e,n,o){po(e,nr(e),t,o)})),zs=fo((function(t,e,n,o){po(e,er(e),t,o)})),Ys=Eo(Pe),Vs=Un((function(t,e){t=_r(t);var n=-1,o=e.length,a=o>2?e[2]:F;for(a&&Zo(e[0],e[1],a)&&(o=1);++n<o;)for(var r=e[n],i=nr(r),s=-1,l=i.length;++s<l;){var u=i[s],c=t[u];(c===F||xa(c,Ar[u])&&!Tr.call(t,u))&&(t[u]=r[u])}return t})),Ws=Un((function(e){return e.push(F,Ho),t(Qs,F,e)})),Gs=Lo((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=xr.call(e)),t[e]=n}),lr(ur)),qs=Lo((function(t,e,n){null!=e&&"function"!=typeof e.toString&&(e=xr.call(e)),Tr.call(t,e)?t[e].push(n):t[e]=[n]}),Yo),Js=Un(on),Ks=fo((function(t,e,n){An(t,e,n)})),Qs=fo((function(t,e,n,o){An(t,e,n,o)})),Zs=Eo((function(t,e){var n={};if(null==t)return n;var o=!1;e=l(e,(function(e){return e=no(e,t),o||(o=e.length>1),e})),po(t,Bo(t),n),o&&(n=Te(n,7,$o));for(var a=e.length;a--;)qn(n,e[a]);return n})),Xs=Eo((function(t,e){return null==t?{}:function(t,e){return Tn(t,e,(function(e,n){return tr(t,n)}))}(t,e)})),tl=xo(er),el=xo(nr),nl=yo((function(t,e,n){return e=e.toLowerCase(),t+(n?rr(e):e)})),ol=yo((function(t,e,n){return t+(n?"-":"")+e.toLowerCase()})),al=yo((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),rl=vo("toLowerCase"),il=yo((function(t,e,n){return t+(n?"_":"")+e.toLowerCase()})),sl=yo((function(t,e,n){return t+(n?" ":"")+ul(e)})),ll=yo((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()})),ul=vo("toUpperCase"),cl=Un((function(e,n){try{return t(e,F,n)}catch(t){return Ha(t)?t:new yr(t)}})),dl=Eo((function(t,e){return n(e,(function(e){e=ca(e),Ae(t,e,bs(t[e],t))})),t})),pl=ko(),ml=ko(!0),fl=Un((function(t,e){return function(n){return on(n,t,e)}})),gl=Un((function(t,e){return function(n){return on(t,n,e)}})),hl=Co(l),vl=Co(a),yl=Co(p),bl=jo(),wl=jo(!0),_l=So((function(t,e){return t+e}),0),kl=Mo("ceil"),Il=So((function(t,e){return t/e}),1),Ll=Mo("floor"),Sl=So((function(t,e){return t*e}),1),Cl=Mo("round"),Al=So((function(t,e){return t-e}),0);return Qt.after=function(t,e){if("function"!=typeof e)throw new Lr(z);return t=qa(t),function(){if(--t<1)return e.apply(this,arguments)}},Qt.ary=Pa,Qt.assign=Ns,Qt.assignIn=Bs,Qt.assignInWith=Fs,Qt.assignWith=zs,Qt.at=Ys,Qt.before=ja,Qt.bind=bs,Qt.bindAll=dl,Qt.bindKey=ws,Qt.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return Ts(t)?t:[t]},Qt.chain=Ia,Qt.chunk=function(t,e,n){e=(n?Zo(t,e,n):e===F)?1:ri(qa(e),0);var o=null==t?0:t.length;if(!o||e<1)return[];for(var a=0,r=0,i=hr(Zr(o/e));a<o;)i[r++]=Nn(t,a,a+=e);return i},Qt.compact=function(t){for(var e=-1,n=null==t?0:t.length,o=0,a=[];++e<n;){var r=t[e];r&&(a[o++]=r)}return a},Qt.concat=function(){var t=arguments.length;if(!t)return[];for(var e=hr(t-1),n=arguments[0],o=t;o--;)e[o-1]=arguments[o];return u(Ts(n)?co(n):[n],$e(e,1))},Qt.cond=function(e){var n=null==e?0:e.length,o=Yo();return e=n?l(e,(function(t){if("function"!=typeof t[1])throw new Lr(z);return[o(t[0]),t[1]]})):[],Un((function(o){for(var a=-1;++a<n;){var r=e[a];if(t(r[0],this,o))return t(r[1],this,o)}}))},Qt.conforms=function(t){return function(t){var e=er(t);return function(n){return De(n,t,e)}}(Te(t,1))},Qt.constant=lr,Qt.countBy=cs,Qt.create=function(t,e){var n=Ai(t);return null==e?n:Ce(n,e)},Qt.curry=function t(e,n,o){var a=Ro(e,8,F,F,F,F,F,n=o?F:n);return a.placeholder=t.placeholder,a},Qt.curryRight=function t(e,n,o){var a=Ro(e,W,F,F,F,F,F,n=o?F:n);return a.placeholder=t.placeholder,a},Qt.debounce=Ta,Qt.defaults=Vs,Qt.defaultsDeep=Ws,Qt.defer=_s,Qt.delay=ks,Qt.difference=Wi,Qt.differenceBy=Gi,Qt.differenceWith=qi,Qt.drop=function(t,e,n){var o=null==t?0:t.length;return o?Nn(t,(e=n||e===F?1:qa(e))<0?0:e,o):[]},Qt.dropRight=function(t,e,n){var o=null==t?0:t.length;return o?Nn(t,0,(e=o-(e=n||e===F?1:qa(e)))<0?0:e):[]},Qt.dropRightWhile=function(t,e){return t&&t.length?Kn(t,Yo(e,3),!0,!0):[]},Qt.dropWhile=function(t,e){return t&&t.length?Kn(t,Yo(e,3),!0):[]},Qt.fill=function(t,e,n,o){var a=null==t?0:t.length;return a?(n&&"number"!=typeof n&&Zo(t,e,n)&&(n=0,o=a),function(t,e,n,o){var a=t.length;for((n=qa(n))<0&&(n=-n>a?0:a+n),(o=o===F||o>a?a:qa(o))<0&&(o+=a),o=n>o?0:Ja(o);n<o;)t[n++]=e;return t}(t,e,n,o)):[]},Qt.filter=function(t,e){return(Ts(t)?r:He)(t,Yo(e,3))},Qt.flatMap=function(t,e){return $e(Aa(t,e),1)},Qt.flatMapDeep=function(t,e){return $e(Aa(t,e),Q)},Qt.flatMapDepth=function(t,e,n){return n=n===F?1:qa(n),$e(Aa(t,e),n)},Qt.flatten=ha,Qt.flattenDeep=function(t){return null!=t&&t.length?$e(t,Q):[]},Qt.flattenDepth=function(t,e){return null!=t&&t.length?$e(t,e=e===F?1:qa(e)):[]},Qt.flip=function(t){return Ro(t,512)},Qt.flow=pl,Qt.flowRight=ml,Qt.fromPairs=function(t){for(var e=-1,n=null==t?0:t.length,o={};++e<n;){var a=t[e];o[a[0]]=a[1]}return o},Qt.functions=function(t){return null==t?[]:Fe(t,er(t))},Qt.functionsIn=function(t){return null==t?[]:Fe(t,nr(t))},Qt.groupBy=ms,Qt.initial=function(t){return null!=t&&t.length?Nn(t,0,-1):[]},Qt.intersection=Ji,Qt.intersectionBy=Ki,Qt.intersectionWith=Qi,Qt.invert=Gs,Qt.invertBy=qs,Qt.invokeMap=fs,Qt.iteratee=cr,Qt.keyBy=gs,Qt.keys=er,Qt.keysIn=nr,Qt.map=Aa,Qt.mapKeys=function(t,e){var n={};return e=Yo(e,3),Oe(t,(function(t,o,a){Ae(n,e(t,o,a),t)})),n},Qt.mapValues=function(t,e){var n={};return e=Yo(e,3),Oe(t,(function(t,o,a){Ae(n,o,e(t,o,a))})),n},Qt.matches=function(t){return Sn(Te(t,1))},Qt.matchesProperty=function(t,e){return Cn(t,Te(e,1))},Qt.memoize=Da,Qt.merge=Ks,Qt.mergeWith=Qs,Qt.method=fl,Qt.methodOf=gl,Qt.mixin=dr,Qt.negate=Ma,Qt.nthArg=function(t){return t=qa(t),Un((function(e){return Pn(e,t)}))},Qt.omit=Zs,Qt.omitBy=function(t,e){return or(t,Ma(Yo(e)))},Qt.once=function(t){return ja(2,t)},Qt.orderBy=function(t,e,n,o){return null==t?[]:(Ts(e)||(e=null==e?[]:[e]),Ts(n=o?F:n)||(n=null==n?[]:[n]),jn(t,e,n))},Qt.over=hl,Qt.overArgs=Is,Qt.overEvery=vl,Qt.overSome=yl,Qt.partial=Ls,Qt.partialRight=Ss,Qt.partition=hs,Qt.pick=Xs,Qt.pickBy=or,Qt.property=mr,Qt.propertyOf=function(t){return function(e){return null==t?F:ze(t,e)}},Qt.pull=Zi,Qt.pullAll=ba,Qt.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?Dn(t,e,Yo(n,2)):t},Qt.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?Dn(t,e,F,n):t},Qt.pullAt=Xi,Qt.range=bl,Qt.rangeRight=wl,Qt.rearg=Cs,Qt.reject=function(t,e){return(Ts(t)?r:He)(t,Ma(Yo(e,3)))},Qt.remove=function(t,e){var n=[];if(!t||!t.length)return n;var o=-1,a=[],r=t.length;for(e=Yo(e,3);++o<r;){var i=t[o];e(i,o,t)&&(n.push(i),a.push(o))}return Mn(t,a),n},Qt.rest=function(t,e){if("function"!=typeof t)throw new Lr(z);return Un(t,e=e===F?e:qa(e))},Qt.reverse=wa,Qt.sampleSize=function(t,e,n){return e=(n?Zo(t,e,n):e===F)?1:qa(e),(Ts(t)?we:$n)(t,e)},Qt.set=function(t,e,n){return null==t?t:On(t,e,n)},Qt.setWith=function(t,e,n,o){return o="function"==typeof o?o:F,null==t?t:On(t,e,n,o)},Qt.shuffle=function(t){return(Ts(t)?_e:En)(t)},Qt.slice=function(t,e,n){var o=null==t?0:t.length;return o?(n&&"number"!=typeof n&&Zo(t,e,n)?(e=0,n=o):(e=null==e?0:qa(e),n=n===F?o:qa(n)),Nn(t,e,n)):[]},Qt.sortBy=vs,Qt.sortedUniq=function(t){return t&&t.length?Yn(t):[]},Qt.sortedUniqBy=function(t,e){return t&&t.length?Yn(t,Yo(e,2)):[]},Qt.split=function(t,e,n){return n&&"number"!=typeof n&&Zo(t,e,n)&&(e=n=F),(n=n===F?tt:n>>>0)?(t=Za(t))&&("string"==typeof e||null!=e&&!Us(e))&&(!(e=Wn(e))&&M(t))?oo(E(t),0,n):t.split(e,n):[]},Qt.spread=function(e,n){if("function"!=typeof e)throw new Lr(z);return n=null==n?0:ri(qa(n),0),Un((function(o){var a=o[n],r=oo(o,0,n);return a&&u(r,a),t(e,this,r)}))},Qt.tail=function(t){var e=null==t?0:t.length;return e?Nn(t,1,e):[]},Qt.take=function(t,e,n){return t&&t.length?Nn(t,0,(e=n||e===F?1:qa(e))<0?0:e):[]},Qt.takeRight=function(t,e,n){var o=null==t?0:t.length;return o?Nn(t,(e=o-(e=n||e===F?1:qa(e)))<0?0:e,o):[]},Qt.takeRightWhile=function(t,e){return t&&t.length?Kn(t,Yo(e,3),!1,!0):[]},Qt.takeWhile=function(t,e){return t&&t.length?Kn(t,Yo(e,3)):[]},Qt.tap=function(t,e){return e(t),t},Qt.throttle=function(t,e,n){var o=!0,a=!0;if("function"!=typeof t)throw new Lr(z);return Na(n)&&(o="leading"in n?!!n.leading:o,a="trailing"in n?!!n.trailing:a),Ta(t,e,{leading:o,maxWait:e,trailing:a})},Qt.thru=La,Qt.toArray=Wa,Qt.toPairs=tl,Qt.toPairsIn=el,Qt.toPath=function(t){return Ts(t)?l(t,ca):Va(t)?[t]:co(Vi(Za(t)))},Qt.toPlainObject=Qa,Qt.transform=function(t,e,o){var a=Ts(t),r=a||Ms(t)||$s(t);if(e=Yo(e,4),null==o){var i=t&&t.constructor;o=r?a?new i:[]:Na(t)&&$a(i)?Ai(Br(t)):{}}return(r?n:Oe)(t,(function(t,n,a){return e(o,t,n,a)})),o},Qt.unary=function(t){return Pa(t,1)},Qt.union=ts,Qt.unionBy=es,Qt.unionWith=ns,Qt.uniq=function(t){return t&&t.length?Gn(t):[]},Qt.uniqBy=function(t,e){return t&&t.length?Gn(t,Yo(e,2)):[]},Qt.uniqWith=function(t,e){return e="function"==typeof e?e:F,t&&t.length?Gn(t,F,e):[]},Qt.unset=function(t,e){return null==t||qn(t,e)},Qt.unzip=_a,Qt.unzipWith=ka,Qt.update=function(t,e,n){return null==t?t:Jn(t,e,eo(n))},Qt.updateWith=function(t,e,n,o){return o="function"==typeof o?o:F,null==t?t:Jn(t,e,eo(n),o)},Qt.values=ar,Qt.valuesIn=function(t){return null==t?[]:A(t,nr(t))},Qt.without=os,Qt.words=sr,Qt.wrap=function(t,e){return Ls(eo(e),t)},Qt.xor=as,Qt.xorBy=rs,Qt.xorWith=is,Qt.zip=ss,Qt.zipObject=function(t,e){return Xn(t||[],e||[],Ie)},Qt.zipObjectDeep=function(t,e){return Xn(t||[],e||[],On)},Qt.zipWith=ls,Qt.entries=tl,Qt.entriesIn=el,Qt.extend=Bs,Qt.extendWith=Fs,dr(Qt,Qt),Qt.add=_l,Qt.attempt=cl,Qt.camelCase=nl,Qt.capitalize=rr,Qt.ceil=kl,Qt.clamp=function(t,e,n){return n===F&&(n=e,e=F),n!==F&&(n=(n=Ka(n))==n?n:0),e!==F&&(e=(e=Ka(e))==e?e:0),je(Ka(t),e,n)},Qt.clone=function(t){return Te(t,4)},Qt.cloneDeep=function(t){return Te(t,5)},Qt.cloneDeepWith=function(t,e){return Te(t,5,e="function"==typeof e?e:F)},Qt.cloneWith=function(t,e){return Te(t,4,e="function"==typeof e?e:F)},Qt.conformsTo=function(t,e){return null==e||De(t,e,er(e))},Qt.deburr=ir,Qt.defaultTo=function(t,e){return null==t||t!=t?e:t},Qt.divide=Il,Qt.endsWith=function(t,e,n){t=Za(t),e=Wn(e);var o=t.length,a=n=n===F?o:je(qa(n),0,o);return(n-=e.length)>=0&&t.slice(n,a)==e},Qt.eq=xa,Qt.escape=function(t){return(t=Za(t))&&$t.test(t)?t.replace(Ut,hn):t},Qt.escapeRegExp=function(t){return(t=Za(t))&&Vt.test(t)?t.replace(Yt,"\\$&"):t},Qt.every=function(t,e,n){var o=Ts(t)?a:Re;return n&&Zo(t,e,n)&&(e=F),o(t,Yo(e,3))},Qt.find=ds,Qt.findIndex=fa,Qt.findKey=function(t,e){return f(t,Yo(e,3),Oe)},Qt.findLast=ps,Qt.findLastIndex=ga,Qt.findLastKey=function(t,e){return f(t,Yo(e,3),Ee)},Qt.floor=Ll,Qt.forEach=Sa,Qt.forEachRight=Ca,Qt.forIn=function(t,e){return null==t?t:Ti(t,Yo(e,3),nr)},Qt.forInRight=function(t,e){return null==t?t:Di(t,Yo(e,3),nr)},Qt.forOwn=function(t,e){return t&&Oe(t,Yo(e,3))},Qt.forOwnRight=function(t,e){return t&&Ee(t,Yo(e,3))},Qt.get=Xa,Qt.gt=As,Qt.gte=Ps,Qt.has=function(t,e){return null!=t&&qo(t,e,Xe)},Qt.hasIn=tr,Qt.head=va,Qt.identity=ur,Qt.includes=function(t,e,n,o){t=Ra(t)?t:ar(t),n=n&&!o?qa(n):0;var a=t.length;return n<0&&(n=ri(a+n,0)),Ya(t)?n<=a&&t.indexOf(e,n)>-1:!!a&&h(t,e,n)>-1},Qt.indexOf=function(t,e,n){var o=null==t?0:t.length;if(!o)return-1;var a=null==n?0:qa(n);return a<0&&(a=ri(o+a,0)),h(t,e,a)},Qt.inRange=function(t,e,n){return e=Ga(e),n===F?(n=e,e=0):n=Ga(n),function(t,e,n){return t>=ii(e,n)&&t<ri(e,n)}(t=Ka(t),e,n)},Qt.invoke=Js,Qt.isArguments=js,Qt.isArray=Ts,Qt.isArrayBuffer=Ds,Qt.isArrayLike=Ra,Qt.isArrayLikeObject=Ua,Qt.isBoolean=function(t){return!0===t||!1===t||Ba(t)&&Ve(t)==it},Qt.isBuffer=Ms,Qt.isDate=xs,Qt.isElement=function(t){return Ba(t)&&1===t.nodeType&&!za(t)},Qt.isEmpty=function(t){if(null==t)return!0;if(Ra(t)&&(Ts(t)||"string"==typeof t||"function"==typeof t.splice||Ms(t)||$s(t)||js(t)))return!t.length;var e=Ni(t);if(e==dt||e==ht)return!t.size;if(ea(t))return!_n(t).length;for(var n in t)if(Tr.call(t,n))return!1;return!0},Qt.isEqual=function(t,e){return sn(t,e)},Qt.isEqualWith=function(t,e,n){var o=(n="function"==typeof n?n:F)?n(t,e):F;return o===F?sn(t,e,F,n):!!o},Qt.isError=Ha,Qt.isFinite=function(t){return"number"==typeof t&&ni(t)},Qt.isFunction=$a,Qt.isInteger=Oa,Qt.isLength=Ea,Qt.isMap=Rs,Qt.isMatch=function(t,e){return t===e||fn(t,e,Wo(e))},Qt.isMatchWith=function(t,e,n){return n="function"==typeof n?n:F,fn(t,e,Wo(e),n)},Qt.isNaN=function(t){return Fa(t)&&t!=+t},Qt.isNative=function(t){if(Bi(t))throw new yr("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(t)},Qt.isNil=function(t){return null==t},Qt.isNull=function(t){return null===t},Qt.isNumber=Fa,Qt.isObject=Na,Qt.isObjectLike=Ba,Qt.isPlainObject=za,Qt.isRegExp=Us,Qt.isSafeInteger=function(t){return Oa(t)&&t>=-Z&&t<=Z},Qt.isSet=Hs,Qt.isString=Ya,Qt.isSymbol=Va,Qt.isTypedArray=$s,Qt.isUndefined=function(t){return t===F},Qt.isWeakMap=function(t){return Ba(t)&&Ni(t)==bt},Qt.isWeakSet=function(t){return Ba(t)&&"[object WeakSet]"==Ve(t)},Qt.join=function(t,e){return null==t?"":oi.call(t,e)},Qt.kebabCase=ol,Qt.last=ya,Qt.lastIndexOf=function(t,e,n){var o=null==t?0:t.length;if(!o)return-1;var a=o;return n!==F&&(a=(a=qa(n))<0?ri(o+a,0):ii(a,o-1)),e==e?function(t,e,n){for(var o=n+1;o--;)if(t[o]===e)return o;return o}(t,e,a):g(t,y,a,!0)},Qt.lowerCase=al,Qt.lowerFirst=rl,Qt.lt=Os,Qt.lte=Es,Qt.max=function(t){return t&&t.length?Ue(t,ur,Ke):F},Qt.maxBy=function(t,e){return t&&t.length?Ue(t,Yo(e,2),Ke):F},Qt.mean=function(t){return b(t,ur)},Qt.meanBy=function(t,e){return b(t,Yo(e,2))},Qt.min=function(t){return t&&t.length?Ue(t,ur,In):F},Qt.minBy=function(t,e){return t&&t.length?Ue(t,Yo(e,2),In):F},Qt.stubArray=fr,Qt.stubFalse=gr,Qt.stubObject=function(){return{}},Qt.stubString=function(){return""},Qt.stubTrue=function(){return!0},Qt.multiply=Sl,Qt.nth=function(t,e){return t&&t.length?Pn(t,qa(e)):F},Qt.noConflict=function(){return en._===this&&(en._=Ur),this},Qt.noop=pr,Qt.now=ys,Qt.pad=function(t,e,n){t=Za(t);var o=(e=qa(e))?O(t):0;if(!e||o>=e)return t;var a=(e-o)/2;return Ao(Xr(a),n)+t+Ao(Zr(a),n)},Qt.padEnd=function(t,e,n){t=Za(t);var o=(e=qa(e))?O(t):0;return e&&o<e?t+Ao(e-o,n):t},Qt.padStart=function(t,e,n){t=Za(t);var o=(e=qa(e))?O(t):0;return e&&o<e?Ao(e-o,n)+t:t},Qt.parseInt=function(t,e,n){return n||null==e?e=0:e&&(e=+e),li(Za(t).replace(Wt,""),e||0)},Qt.random=function(t,e,n){if(n&&"boolean"!=typeof n&&Zo(t,e,n)&&(e=n=F),n===F&&("boolean"==typeof e?(n=e,e=F):"boolean"==typeof t&&(n=t,t=F)),t===F&&e===F?(t=0,e=1):(t=Ga(t),e===F?(e=t,t=0):e=Ga(e)),t>e){var o=t;t=e,e=o}if(n||t%1||e%1){var a=ui();return ii(t+a*(e-t+Qe("1e-"+((a+"").length-1))),e)}return xn(t,e)},Qt.reduce=function(t,e,n){var o=Ts(t)?c:k,a=arguments.length<3;return o(t,Yo(e,4),n,a,Pi)},Qt.reduceRight=function(t,e,n){var o=Ts(t)?d:k,a=arguments.length<3;return o(t,Yo(e,4),n,a,ji)},Qt.repeat=function(t,e,n){return e=(n?Zo(t,e,n):e===F)?1:qa(e),Rn(Za(t),e)},Qt.replace=function(){var t=arguments,e=Za(t[0]);return t.length<3?e:e.replace(t[1],t[2])},Qt.result=function(t,e,n){var o=-1,a=(e=no(e,t)).length;for(a||(a=1,t=F);++o<a;){var r=null==t?F:t[ca(e[o])];r===F&&(o=a,r=n),t=$a(r)?r.call(t):r}return t},Qt.round=Cl,Qt.runInContext=_,Qt.sample=function(t){return(Ts(t)?be:Hn)(t)},Qt.size=function(t){if(null==t)return 0;if(Ra(t))return Ya(t)?O(t):t.length;var e=Ni(t);return e==dt||e==ht?t.size:_n(t).length},Qt.snakeCase=il,Qt.some=function(t,e,n){var o=Ts(t)?p:Bn;return n&&Zo(t,e,n)&&(e=F),o(t,Yo(e,3))},Qt.sortedIndex=function(t,e){return Fn(t,e)},Qt.sortedIndexBy=function(t,e,n){return zn(t,e,Yo(n,2))},Qt.sortedIndexOf=function(t,e){var n=null==t?0:t.length;if(n){var o=Fn(t,e);if(o<n&&xa(t[o],e))return o}return-1},Qt.sortedLastIndex=function(t,e){return Fn(t,e,!0)},Qt.sortedLastIndexBy=function(t,e,n){return zn(t,e,Yo(n,2),!0)},Qt.sortedLastIndexOf=function(t,e){if(null!=t&&t.length){var n=Fn(t,e,!0)-1;if(xa(t[n],e))return n}return-1},Qt.startCase=sl,Qt.startsWith=function(t,e,n){return t=Za(t),n=null==n?0:je(qa(n),0,t.length),e=Wn(e),t.slice(n,n+e.length)==e},Qt.subtract=Al,Qt.sum=function(t){return t&&t.length?I(t,ur):0},Qt.sumBy=function(t,e){return t&&t.length?I(t,Yo(e,2)):0},Qt.template=function(t,e,n){var o=Qt.templateSettings;n&&Zo(t,e,n)&&(e=F),t=Za(t),e=Fs({},e,o,Uo);var a,r,i=Fs({},e.imports,o.imports,Uo),s=er(i),l=A(i,s),u=0,c=e.interpolate||le,d="__p += '",p=kr((e.escape||le).source+"|"+c.source+"|"+(c===Nt?te:le).source+"|"+(e.evaluate||le).source+"|$","g"),m="//# sourceURL="+(Tr.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Ge+"]")+"\n";t.replace(p,(function(e,n,o,i,s,l){return o||(o=i),d+=t.slice(u,l).replace(ue,D),n&&(a=!0,d+="' +\n__e("+n+") +\n'"),s&&(r=!0,d+="';\n"+s+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),u=l+e.length,e})),d+="';\n";var f=Tr.call(e,"variable")&&e.variable;if(f){if(Zt.test(f))throw new yr("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(r?d.replace(Dt,""):d).replace(Mt,"$1").replace(xt,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(a?", __e = _.escape":"")+(r?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var g=cl((function(){return br(s,m+"return "+d).apply(F,l)}));if(g.source=d,Ha(g))throw g;return g},Qt.times=function(t,e){if((t=qa(t))<1||t>Z)return[];var n=tt,o=ii(t,tt);e=Yo(e),t-=tt;for(var a=L(o,e);++n<t;)e(n);return a},Qt.toFinite=Ga,Qt.toInteger=qa,Qt.toLength=Ja,Qt.toLower=function(t){return Za(t).toLowerCase()},Qt.toNumber=Ka,Qt.toSafeInteger=function(t){return t?je(qa(t),-Z,Z):0===t?t:0},Qt.toString=Za,Qt.toUpper=function(t){return Za(t).toUpperCase()},Qt.trim=function(t,e,n){if((t=Za(t))&&(n||e===F))return S(t);if(!t||!(e=Wn(e)))return t;var o=E(t),a=E(e);return oo(o,j(o,a),T(o,a)+1).join("")},Qt.trimEnd=function(t,e,n){if((t=Za(t))&&(n||e===F))return t.slice(0,N(t)+1);if(!t||!(e=Wn(e)))return t;var o=E(t);return oo(o,0,T(o,E(e))+1).join("")},Qt.trimStart=function(t,e,n){if((t=Za(t))&&(n||e===F))return t.replace(Wt,"");if(!t||!(e=Wn(e)))return t;var o=E(t);return oo(o,j(o,E(e))).join("")},Qt.truncate=function(t,e){var n=30,o="...";if(Na(e)){var a="separator"in e?e.separator:a;n="length"in e?qa(e.length):n,o="omission"in e?Wn(e.omission):o}var r=(t=Za(t)).length;if(M(t)){var i=E(t);r=i.length}if(n>=r)return t;var s=n-O(o);if(s<1)return o;var l=i?oo(i,0,s).join(""):t.slice(0,s);if(a===F)return l+o;if(i&&(s+=l.length-s),Us(a)){if(t.slice(s).search(a)){var u,c=l;for(a.global||(a=kr(a.source,Za(ee.exec(a))+"g")),a.lastIndex=0;u=a.exec(c);)var d=u.index;l=l.slice(0,d===F?s:d)}}else if(t.indexOf(Wn(a),s)!=s){var p=l.lastIndexOf(a);p>-1&&(l=l.slice(0,p))}return l+o},Qt.unescape=function(t){return(t=Za(t))&&Ht.test(t)?t.replace(Rt,vn):t},Qt.uniqueId=function(t){var e=++Dr;return Za(t)+e},Qt.upperCase=ll,Qt.upperFirst=ul,Qt.each=Sa,Qt.eachRight=Ca,Qt.first=va,dr(Qt,function(){var t={};return Oe(Qt,(function(e,n){Tr.call(Qt.prototype,n)||(t[n]=e)})),t}(),{chain:!1}),Qt.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){Qt[t].placeholder=Qt})),n(["drop","take"],(function(t,e){pe.prototype[t]=function(n){n=n===F?1:ri(qa(n),0);var o=this.__filtered__&&!e?new pe(this):this.clone();return o.__filtered__?o.__takeCount__=ii(n,o.__takeCount__):o.__views__.push({size:ii(n,tt),type:t+(o.__dir__<0?"Right":"")}),o},pe.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),n(["filter","map","takeWhile"],(function(t,e){var n=e+1,o=1==n||3==n;pe.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:Yo(t,3),type:n}),e.__filtered__=e.__filtered__||o,e}})),n(["head","last"],(function(t,e){var n="take"+(e?"Right":"");pe.prototype[t]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(t,e){var n="drop"+(e?"":"Right");pe.prototype[t]=function(){return this.__filtered__?new pe(this):this[n](1)}})),pe.prototype.compact=function(){return this.filter(ur)},pe.prototype.find=function(t){return this.filter(t).head()},pe.prototype.findLast=function(t){return this.reverse().find(t)},pe.prototype.invokeMap=Un((function(t,e){return"function"==typeof t?new pe(this):this.map((function(n){return on(n,t,e)}))})),pe.prototype.reject=function(t){return this.filter(Ma(Yo(t)))},pe.prototype.slice=function(t,e){t=qa(t);var n=this;return n.__filtered__&&(t>0||e<0)?new pe(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),e!==F&&(n=(e=qa(e))<0?n.dropRight(-e):n.take(e-t)),n)},pe.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},pe.prototype.toArray=function(){return this.take(tt)},Oe(pe.prototype,(function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),o=/^(?:head|last)$/.test(e),a=Qt[o?"take"+("last"==e?"Right":""):e],r=o||/^find/.test(e);a&&(Qt.prototype[e]=function(){var e=this.__wrapped__,i=o?[1]:arguments,s=e instanceof pe,l=i[0],c=s||Ts(e),d=function(t){var e=a.apply(Qt,u([t],i));return o&&p?e[0]:e};c&&n&&"function"==typeof l&&1!=l.length&&(s=c=!1);var p=this.__chain__,m=!!this.__actions__.length,f=r&&!p,g=s&&!m;if(!r&&c){e=g?e:new pe(this);var h=t.apply(e,i);return h.__actions__.push({func:La,args:[d],thisArg:F}),new de(h,p)}return f&&g?t.apply(this,i):(h=this.thru(d),f?o?h.value()[0]:h.value():h)})})),n(["pop","push","shift","sort","splice","unshift"],(function(t){var e=Sr[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",o=/^(?:pop|shift)$/.test(t);Qt.prototype[t]=function(){var t=arguments;if(o&&!this.__chain__){var a=this.value();return e.apply(Ts(a)?a:[],t)}return this[n]((function(n){return e.apply(Ts(n)?n:[],t)}))}})),Oe(pe.prototype,(function(t,e){var n=Qt[e];if(n){var o=n.name+"";Tr.call(yi,o)||(yi[o]=[]),yi[o].push({name:e,func:n})}})),yi[Io(F,2).name]=[{name:"wrapper",func:F}],pe.prototype.clone=function(){var t=new pe(this.__wrapped__);return t.__actions__=co(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=co(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=co(this.__views__),t},pe.prototype.reverse=function(){if(this.__filtered__){var t=new pe(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},pe.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=Ts(t),o=e<0,a=n?t.length:0,r=function(t,e,n){for(var o=-1,a=n.length;++o<a;){var r=n[o],i=r.size;switch(r.type){case"drop":t+=i;break;case"dropRight":e-=i;break;case"take":e=ii(e,t+i);break;case"takeRight":t=ri(t,e-i)}}return{start:t,end:e}}(0,a,this.__views__),i=r.start,s=r.end,l=s-i,u=o?s:i-1,c=this.__iteratees__,d=c.length,p=0,m=ii(l,this.__takeCount__);if(!n||!o&&a==l&&m==l)return Qn(t,this.__actions__);var f=[];t:for(;l--&&p<m;){for(var g=-1,h=t[u+=e];++g<d;){var v=c[g],y=v.iteratee,b=v.type,w=y(h);if(2==b)h=w;else if(!w){if(1==b)continue t;break t}}f[p++]=h}return f},Qt.prototype.at=us,Qt.prototype.chain=function(){return Ia(this)},Qt.prototype.commit=function(){return new de(this.value(),this.__chain__)},Qt.prototype.next=function(){this.__values__===F&&(this.__values__=Wa(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?F:this.__values__[this.__index__++]}},Qt.prototype.plant=function(t){for(var e,n=this;n instanceof ce;){var o=ma(n);o.__index__=0,o.__values__=F,e?a.__wrapped__=o:e=o;var a=o;n=n.__wrapped__}return a.__wrapped__=t,e},Qt.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof pe){var e=t;return this.__actions__.length&&(e=new pe(this)),(e=e.reverse()).__actions__.push({func:La,args:[wa],thisArg:F}),new de(e,this.__chain__)}return this.thru(wa)},Qt.prototype.toJSON=Qt.prototype.valueOf=Qt.prototype.value=function(){return Qn(this.__wrapped__,this.__actions__)},Qt.prototype.first=Qt.prototype.head,Wr&&(Qt.prototype[Wr]=function(){return this}),Qt}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(en._=yn,define((function(){return yn}))):on?((on.exports=yn)._=yn,nn._=yn):en._=yn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div :class="[header.isMenuOpen ? \'menuOpen\' : \'\']">\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(t){this.$emit("onUserInfo",t)},onMini(t){this.isMiniSidebar=t}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(t,e){t!==e&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(t,e){"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(t){this.updateActiveOrg(t.id)},orgUpdated(t){const e=t?.response?.data;201===e?.code?(sessionStorage.clear(),window.location.reload(!0)):e?.message&&console.log(e.message)},updateActiveOrg(t){this.$buefy.loading.open();const e={apiURL:YUNOCommon.config.academy("activeOrg",!1),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:t},callback:!0,callbackFunc:t=>this.orgUpdated(t)};this.dispatchData("postData",e)},dispatchData(t,e){this.$store.dispatch(t,e)},init(){if("org-admin"===this.userInfo.data.role){const t=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=t}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t}}});const YUNOCommon=function(t){const e={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let t=window.location.hostname;return"localhost"===t?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===t?"https://api.yunolearning.com":"stage.yunolearning.com"===t||"dev.yunolearning.com"===t?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===t?"https://api.yunolearning.com":void 0},addVerion:function(t){let e="";return e=t?"?buildVersion=1":"&buildVersion=1",e},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":this.host()},laravelHost:function(){return"http://localhost"===this.host()||"https://dev.yunolearning.com"===this.host()?"https://vcdev.yunolearning.com":"https://stage.yunolearning.com"===this.host()?"https://vcstage.yunolearning.com":"https://www.yunolearning.com"===this.host()?"https://ai-laravel-yxdza.kinsta.app":void 0},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(t,e){let n="";void 0!==e&&(n="?category="+e);return this.pickHost()+"/wp-json/yuno/v1/menu/"+t+n},headerMenuAPIV2:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/menu/"+t+"/"+e},userRoleAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/users/"+t+"/role"},userProfileAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/users/"+t+"/profile"+n},studentResultsAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+t+"/"+e+"/"+n},faqAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/faq/"+t},courseAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+t+"/"+e},scheduleAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/courses/"+t+"/schedule"},instructorAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+t+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+t+"/"+e},instructorBatchAPI:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/batches/"+t+"/instructor/"+e+"/"+n+"/"+o+"/"+a+"/"+r},instructorNonBatchesAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+t},categoriesAPi:function(t){void 0===t&&(t="");return this.pickHost()+"/wp-json/yuno/v1/category"+t},featuredCoursesAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+t},batchAPi:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/"+t+"/"+e+"/"+n+"/"+o},classAPi:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/classes/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},signUpAPi:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/users/"+t+"/"+e},signUpV2APi:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/users/"+t+"/"+e},isUserSignUpAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+t},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/update/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/payment"},myLearnersAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+t},classTitleAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+t},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(t){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+t},updateClassAPi:function(t){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+t},classesAPi:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/classes/"+t+"/"+e+"/"+n+"/"+o+"/"+a},classesByViewAPi:function(t,e,n,a,r,i,s,l,u,c){let d="",p="",m="";c&&(d="?ver="+o()),void 0!==e&&!1!==e&&(p="/"+e),void 0!==i&&!1!==i&&(m="/"+i);return this.pickHost()+"/wp-json/yuno/v2/classes/"+t+p+"/"+n+"/"+a+"/"+r+m+"/"+s+"/"+l+"/"+u+d},groupsAPi:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+t+"/"+e+"/"+n+"/"+o},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(t){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(t,e,n,o,a){let r="";!1!==a&&(r="?filter="+a);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+t+"/"+e+"/"+n+"/"+o+r},instructorBatchesAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+t+"/"+e},learnerCoursesAPI:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+t+"/courses/"+e+a},enHeroCardsAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+t+"/"+e},classLearnerAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+t+"/"+e+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+t+"/instructor/"+e},allCoursesAPI:function(t){let e="";void 0!==t&&!1!==t&&(e="/"+t);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+e},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+t+"/"+e+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+t+"/"+e},paymentList:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/payments/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentList:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},enrollmentsList:function(t,e,n,o,a,r,i,s,l,u,c,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+m+"/"+c+"/"+d},paymentsList:function(t,e,n,a,r,i,s,l,u,c){let d="";c&&(d="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/payment/"+t+"/"+e+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(t,e){let n="",o="";void 0!==e&&!1!==e&&(o="/"+e),void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+o},reviewsByTypeAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+t+"/"+e},batchToggleAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+t+"/"+e+"/"+n+"/status/toggle"},changeBatchAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(t,e){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+t+"&offset="+e+"&_embed"},pageAPI:function(t){return this.pickHost()+"/wp-json/wp/v2/pages/"+t+"?_embed"},blogAPI:function(t){return this.pickHost()+"/wp-json/wp/v2/posts/"+t+"?_embed"},postCategoriesAPI:function(t){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+t},courseBatchesAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+t+"/0/upcomingOngoing"},blogsByCategoryAPI:function(t,e,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+t+"&per_page="+e+"&offset="+n+"&_embed"},blogCategoryAPI:function(t){return this.pickHost()+"/wp-json/wp/v2/categories/"+t},settingsAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+t},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+t+"/address/"+e+a},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+t},meetingAPI:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+t+"/"+e+"/"+n+"/"+o},participantsAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+t},batchesGrid:function(t,e,n,o,a,r,i,s,l){let u="";void 0!==i&&!1!==i&&(u="/"+i);return this.pickHost()+"/wp-json/yuno/v1/batches/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+u+"/"+s+"/"+l},mapCoursesAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+t+"/"+e+"/"+n},updateInstructorCoursesAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+t+"/course/"+e},relatedCoursesAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+t+"/courses"},categoryListAPI:function(t){let e="";void 0!==t&&(e="?filter="+t);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+e},categoryTaxonomyAPI:function(t){let e="";void 0!==t&&!1!==t&&(e="/"+t);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+e},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},deleteResourceAttachmentAPI:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/"+t+"/"+e+"/attachment/delete/"+n},resourceEmailAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/"+t+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/document/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},videoListAPI:function(t,e){let n="",o="";void 0!==t&&(n=t),void 0===o&&!1===o||(o=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(t){let e="";void 0===e&&!1===e||(e=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+e},videoListByViewAPI:function(t,e,n,o,a){let r="";if(!1!==e)r=e;else{let e="";void 0!==n&&(e=n),r=t+"/"+e+"/"+o+"/"+a}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+r},createVideoAPI:function(t){let e="";e=t?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+e},userInfoAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+t+n},vcSettingsAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+t},reviewAPI:function(t,e,n,a,r,i,s){let l="",u="",c="",d="",p="",m="";void 0!==t&&!1!==t&&(l="/"+t),void 0!==a&&!1!==a&&(d="/"+a),void 0!==r&&!1!==r&&(p="/"+r),void 0!==i&&!1!==i&&(m="/"+i),void 0!==n&&!1!==n&&(c="/"+n),s&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+e+c+d+p+m+u},courseListAPI:function(t,e,n,a){let r="",i="",s="";a&&(r="?ver="+o()),void 0!==e&&!1!==e&&(i="/"+e),void 0!==n&&!1!==n&&(s="/"+n);return this.pickHost()+"/wp-json/yuno/v1/all/"+t+"/detail/list"+i+s+r},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+t},cityListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+t},languageListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/"+t+"/batch"},batchDetailAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/batches/"+t+n},learnerListAPI:function(t,e){let n="";e&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+t+n},instructorAvailabilityAPI:function(t,e,n){let a="",r="";n&&(a="?ver="+o()),void 0!==e&&!1!==e&&(r="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+t+r+a},createUpdateAvailabilityAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+t+n},timeSlotsAPI:function(t){let e="";t&&(e="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+e},availabilityGridAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+t+n},instructorsByCategoryAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+t+n},capabilitiesAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+t+n},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+t},invitedByUserAPI:function(t,e){let n="";e&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+t+n},signInURLWithState(t){const e=["email","profile"],n=encodeURI(JSON.stringify(t));let o="";if(void 0!==yunoCognitoLoginURL){const t=new URL(yunoCognitoLoginURL);t.searchParams.set("state",n);o=t.toString()}else o="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+e.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return o},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+t+"/"+e+a},eventDetailAPI:function(t,e,n,a,r){let i="";r&&(i="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+t+"/"+e+"/"+n+"?uuid="+a+i},profileDetailAPI:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/profile/"+t+"/"+e+a},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/page/"+t},resourcesListingAPI:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/resources/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},resourcesDetailAPI:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/resources/"+t+"/"+e+"/"+n+"/"+o},videoTestimonialAPI:function(t,e){let n="";e&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+t+n},createExamResultAPI:function(t,e,n,o){let a="",r="";void 0===o&&!1===o&&(o="v1"),void 0!==e&&!1!==e&&(a="/"+e),void 0!==n&&!1!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/"+o+"/examresult/"+t+a+r},deleteExamResultAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+t},manageVideotestimonialAPI:function(t,e,n){let o="",a="";void 0!==e&&(o="/"+e),void 0!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+t+o+a},videotestimonialListAPI:function(t,e,n,o){let a="",r="";void 0!==n&&(a="/"+n),void 0!==o&&(r="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+t+"/"+e+a+r},deleteVideotestimonialAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+t},manageArticleAPI:function(t,e,n,o,a,r,i){let s="",l="",u="",c="",d="",p="",m="";void 0!==t&&!1!==t&&(u="/"+t),void 0!==e&&!1!==e&&(m="/"+e),void 0!==n&&!1!==n&&(c="/"+n),void 0!==o&&!1!==o&&(d="/"+o),void 0!==a&&!1!==a&&(p="/"+a),void 0!==r&&!1!==r&&(s="/"+r),void 0!==i&&!1!==i&&(l="/"+i);return this.pickHost()+"/wp-json/yuno/v1/article"+u+m+c+d+p+s+l},webinarSingleAPI:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+t+"/"+e+a},webinarListingAPI:function(t,e,n,a,r){let i="";r&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+t+"/"+e+"/"+n+"/"+a+i},deleteWebinarAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+t+"/"+e},webinarEnrollmentAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(t,e,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},notificationListAPI:function(t,e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/"+t},notificationUpdateAPI:function(t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/update"},manageNotificationAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/notification/"+t},searchResourceAPI:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+t+"?search="+o},managelearningContentAPI:function(t,e){let n="";void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+t+n},learningContentAPI:function(t,e,n,o,a){let r="",i="",s="",l="";void 0!==e&&!1!==e&&(r="/"+e),void 0!==n&&!1!==n&&(i="/"+n),void 0!==o&&!1!==o&&(s="/"+o),void 0!==a&&!1!==a&&(l="/"+a);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+t+r+i+s+l},learnerInsightsAPI:function(t,e,n,a,r,i,s,l,u){let c="";u&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+t+"/"+e+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+c},learnerInsightsClassAPI:function(t,e,n,a){let r="",i="";i=void 0!==n&&!1!==n?"v2":"v1",a&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/"+i+"/learner/class/"+t+"/"+e+r},signupFormAPI:function(t,e,n){let a="",r="";n&&(a="?ver="+o()),void 0!==e&&!1!==e&&(r="/?state="+e);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+t+r+a},resourceTitleAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+t+"&item="+e},resourceDraftsAPI:function(t,e,n,a,r){let i="";r&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+t+"/"+e+"/"+n+"/"+a+i},resourceDraftsDeleteAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+t},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(t){return this.pickHost()+"/wp-json/yuno/v2/users/"+t+"/instructor/profile"},subjectsListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/category/"+t+"/"+e},coursesResultsAPI:function(t,e,n,o){let a="";!1!==o&&(a="?filters="+encodeURI(JSON.stringify(o)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+t+"/detail/"+e+"/"+n+"/"+a},resourcesResultsAPI:function(t,e,n,o,a,r,i){let s="";!1!==r&&(s="?filters="+encodeURI(JSON.stringify(r))),void 0!==i&&!1!==i||(i="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+i+"/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+s},instructorStatsAPI:function(t){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+t+"/attendance"},instructorCoursesV2API:function(t,e,n){let a="";n&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+t+"/"+e+a},instructorInsightsAPI:function(t,e,n,a,r,i,s,l){let u="";l&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+t+"/"+e+"/"+n+"/"+a+"/"+r+"/"+i+"/"+s+u},enableDisableInstructorAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+t+"/"+e},vcPermissionAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+t},instructorProfileInsightsAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/"+t+"/users/"+e+"/profile"},piiAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/"+t+"/users/pii/"+e},mappedCoursesAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+t+"/"+e},makeFeaturedAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+t+"/"+e},dashboardLearnersAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+t},manageDashboardAPI:function(t,e){let n="";void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+t+"/report"+n},dashboardListAPI:function(t,e,n,o,a){let r="",i="",s="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==e&&!1!==e&&(s="/"+e),void 0!==o&&!1!==o&&(r="/"+o),void 0!==a&&!1!==a&&(i="/"+a);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+t+s+l+r+i},enrollmentDashboardAPI:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+t+"/"+e+"/"+n+"/"+o+"/"+a},usersListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+t},enrollmentClassDetailAPI:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+t+"/"+e},vimeoVideoAPI:function(t){return"https://api.vimeo.com/videos/"+t},batchLearnersAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+t+"/learners"},courseBatchLearners:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+t+"/"+e+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(t,e){let n="";void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/blog/"+t+n},publishedBlogsAPI:function(t,e,n,o,a,r){let i="",s="",l="",u="",c="",d="";void 0!==t&&!1!==t&&(d="/"+t),void 0!==e&&!1!==e&&(l="/"+e),void 0!==n&&!1!==n&&(u="/"+n),void 0!==o&&!1!==o&&(c="/"+o),void 0!==a&&!1!==a&&(i="/"+a),void 0!==r&&!1!==r&&(s="/"+r);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+u+c+i+s},categoriesListAPI:function(t){return this.pickHost()+"/wp-json/yuno/v1/category/"+t},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(t,e){return"https://api.vimeo.com/videos/"+t+"/privacy/domains/"+e},manageVideoClippingAPI:function(t,e,n,o,a,r){r=void 0!==r&&!1!==r?"clippings":"clipping",e=void 0!==e&&!1!==e?"/"+e:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"";return this.pickHost()+"/wp-json/yuno/v1/"+r+"/"+t+e+n+o+a},instructorMyCourses:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+t+"/"+e+"/"+n},instructorCourseBatches:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+t+"/"+e+"/"+n+"/batches/"+o+"/"+a+"/"+r},manageBookmarkAPI:function(t,e,n,o,a,r){void 0!==e&&!1!==e||(e="v1"),n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",a=void 0!==a&&!1!==a?"/"+a:"",r=void 0!==r&&!1!==r?"?filters="+encodeURI(JSON.stringify(r)):"";return this.pickHost()+"/wp-json/yuno/"+e+"/bookmark/"+t+n+o+a+r},availableCourses:function(t,e,n,o){e=void 0!==e&&!1!==e?"/"+e:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+t+e+n+o},availableBatches:function(t,e,n,o){t=void 0!==t&&!1!==t?"/"+t:"",e=void 0!==e&&!1!==e?"/"+e:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+t+e+n},courseEnrollmentStatus:function(t,e){t=void 0!==t&&!1!==t?"/"+t:"",e=void 0!==e&&!1!==e?"/"+e:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+t+e},courseOneToOne:function(t,e,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},cloudinaryImageUpload:function(t){return!!t&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(t){t=void 0!==t&&!1!==t?"?ids="+encodeURI(JSON.stringify(t)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+t},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+t+"/"+e+"/"+n+"/"+o},pastBatchesList:function(t,e,n,o,a,r){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r},checkout:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+t+"/"+e},instructorInsights:function(t,e,n,o,a,r,i,s,l,u,c,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c+"/"+d+"/"+p},updateNativelanguage:function(t){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+t},endBatch:function(t){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+t},collections:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+t+"/"+e+"/"+n+"/"+o+"/"+a},instructorVideotestimonial:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(t,e,n,o,a,r,i){return this.pickHost()+"/wp-json/yuno/v1/courses/"+t+"/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(t){return this.pickHost()+"/wp-json/yuno/v1/course/"+t+"/subcategories"},courseSchedule:function(t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+t},courseScheduleForm:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+t+"/"+e},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/csv/"+t+"/"+e},courseDetail:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+t+"/"+e},reviewIssues:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+t+"/issue/"+e+"/"+n},reviewPost:function(t){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+t+"/"+e+"/"+n},classReviews:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+t+"/"+e+"/"+n+"/"+o+"/"+a},classReviewsByInstructor:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+t+"/"+e+"/"+n+"/"+o+"/"+a},listOfUser:function(t,e,n){let a="";n&&(a="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/"+e+"/list/"+t+a},learnerActivity:function(t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+t},recentLearnerClass:function(t,e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+t+"/"+e},enrollmentListByType:function(t,e,n,o,a,r,i,s,l,u,c){return this.pickHost()+"/wp-json/yuno/v1/"+t+"/enrollments/"+e+"/"+n+"/"+o+"/"+a+"/"+r+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c},courseEconomics:function(t){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+t},courseEconomicsForm:function(t,e,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+t+"/"+e+"/"+n},courseEconomicsSummary:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+t+"/"+e+"/"+n+"/"+o},csvList:function(t,e,n,o,a){return this.pickHost()+"/wp-json/yuno/v1/csv/"+t+"/"+e+"/"+n+"/"+o+"/"+a},orgList:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/org/"+t},referrerDetails:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(t,e){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+t+"/"+e},mappedInstructors:function(t){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+t},generateRefferralURL:function(t){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(t))},generateRefferralCode:function(t){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(t){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(t,e,n,o){void 0===o&&(o="v1");return this.pickHost()+"/wp-json/yuno/"+o+"/referrer/user/"+t+"/"+e+"/"+n},referralReports:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+t+"/"+e+"/"+n+"/"+o},orgToken:function(t,e){let n="";return n="POST"===e?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===e?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+t,n},webhooks:function(t,e,n,o,a,r){let i="";return"grid"===t?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+e+"/"+n+"/"+o+"/"+a+"/"+r:"create"===t?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===t?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===t&&(i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),i},seo:function(t,e,n,o){let a="";return"status"===t?a=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+e:"markNoIndex"===t?a=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===t&&(a=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+o),a},quiz:function(t,e,n,o){let a="";return void 0!==o&&!1!==o||(o=""),"create"===t?a=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===t?a=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===t?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+e+"/"+o:"quizgrid"===t?a=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===t?a=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===t?a=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===t?a=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+e+"/"+n:"delete"===t?a=this.pickHost()+"/wp-json/yuno/v1/quiz/"+e:"quizReorder"===t&&(a=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),a},question:function(t,e,n,o,a){let r="";return"questions"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+e+"/"+n:"create"===t?r=this.pickHost()+"/wp-json/yuno/v1/question":"single"===t||"delete"===t?r=this.pickHost()+"/wp-json/yuno/v1/question/"+o:"deleteQuestionSet"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"attempt"===t?r=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+o:"attemptQuestionSet"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+a:"questionset"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset/"+a:"questionsetQuestions"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+a:"questionsetQuestionsList"===t?r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+a:"questionsetQuestionsReorder"===t&&(r=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),r},enrollments:function(t,e,n,o){let a="";return"active"===t?a=this.pickHost()+"/wp-json/yuno/v2/batch/"+e+"/"+n+"/learners":"extendDate"===t&&(a=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),a},blog:function(t,e,n,o){let a="";return"recentSingle"===t?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+e:"recentList"===t?a=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+o+"?is_list=true&category_id="+e:"categoriesList"===t?a=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===t&&(a=this.pickHost()+"/wp-json/yuno/v1/blog/"+e),a},writingTask:function(t,e,n,o,a,r,i,s){let l="";return"type"===t?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+e+"/"+n:"create"===t?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===t?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===t?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o+"/"+a+"/"+r+"/"+i+"/"+s:"payload"===t?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o:"delete"===t&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+o),l},categoryLandingPage:function(t,e){let n="";return"category"===t?n=this.pickHost()+"/wp-json/yuno/v2/category/"+e:"practiceTests"===t&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(t,e,n,o){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+t+"/"+e+"/"+n+"/"+o},mainNav:function(t,e){return this.pickHost()+"/wp-json/yuno/v3/menu/"+t+"/"+e},org:function(t,e,n,o,a,r,i,s,l,u,c,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${e}`,singleLearner:`/wp-json/yuno/v1/org/user/${e}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${e}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${o}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${a}/${r}/${i}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${a}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${e}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${s}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${e}/${o}/${a}/${u}/${c}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${e}/${n}/${o}/${a}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${e}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${e}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${e}`}[t]||"")},leadForm:function(t,e,n){let o="";return"steps"===t?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+e+"/"+n:"postStep"===t?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+e+"/"+n:"updateMobile"===t&&(o=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),o},availableCoursesV2:function(t){let e="";return"listing"===t&&(e=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),e},activeCategory:function(t){let e="";return"set"===t&&(e=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),e},learners:function(t,e,n,o,a,r){let i="";switch(t){case"insights":i=`/wp-json/yuno/v2/users/${e}/learner/${n}/${o}/${a}`;break;case"demoRequests":i=`/wp-json/yuno/v1/demo-requests/${e}/list/${o}/${a}`;break;case"demoRequestsOrg":i=`/wp-json/yuno/v1/demo-requests/org-admin/${e}/${n}/${o}/${a}`;break;case"learnerDetailOrg":i=`/wp-json/yuno/v1/demo-requests/${e}/${r}`;break;case"learnerDetail":i=`/wp-json/yuno/v1/demo-requests/${r}`;break;case"instructorLearnerDetail":i=`/wp-json/yuno/v2/instructor/mylearner/${r}`;break;case"orgAdminLearners":i=`/wp-json/yuno/v2/orgadmin/learner/${n}/${o}/${a}`}return this.pickHost()+i},deleteUser:function(t){let e="";return"requested"===t&&(e=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),e},generic:function(t,e,n,o,a){let r="";switch(t){case"googleFonts":r=`https://www.googleapis.com/webfonts/v1/webfonts/?${e}`;break;case"courseSuggestions":r=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${e}`;break;case"contentSearch":r=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${e}`;break;case"userSearch":r=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${e}/${n}/${o}`;break;case"orgBatches":r=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${a}`;break;case"org":r=`${this.pickHost()}/wp-json/yuno/v2/org/${o}`;break;case"categories":r=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return r},course:function(t,e,n,o,a,r){let i="";switch(t){case"payload":i=`${this.pickHost()}/wp-json/yuno/v1/course/${e}`;break;case"updateCourse":i=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${e}/${n}/${o}/${a}/${r}`;break;case"createBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return i},learner:function(t,e,n,o,a){let r="";switch(t){case"enrolledCourses":r=`/wp-json/yuno/v3/learner/${e}/enrollments/${n}`;break;case"classes":r=`/wp-json/yuno/v4/classes/${n}/${o}/${e}?limit=${a.limit}&offset=${a.offset}&course=${a.course}&batch=${a.batch}&academy=${a.academy}`;break;case"filters":r=`/wp-json/yuno/v4/classes/filter/${o}/${e}`;break;case"getClassDetail":r=`/wp-json/yuno/v4/classes/${a.classID}`}return this.pickHost()+r},classes:function(t,e,n,o,a,r,i,s,l,u,c){let d="";if("allClasses"===t)d=`/wp-json/yuno/v3/classes/${e}/${n}/${o}/${a}/${r}/${i}/${s}/${l}/${u}/${c}`;return this.pickHost()+d},instructor:function(t,e){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${e.instructorID}/${e.limit}/${e.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${e.learnerID}`,fetchMyLearners:`/wp-json/yuno/v2/${e.role}/mylearners/${e.instructorID}/${e.view}/${e.limit}/${e.offset}`,schedulePrivateClass:"/wp-json/yuno/v4/classes/private",updatePrivateClass:`/wp-json/yuno/v4/classes/private/${e.classID}`,getClassDetail:`/wp-json/yuno/v4/classes/${e.classID}`,createAvailabilityV2:`/wp-json/yuno/v2/instructor/${e.id}/workinghours/create`,updateAvailabilityV2:`/wp-json/yuno/v2/instructor/${e.id}/workinghours/update`,getInstructorAvailability:`/wp-json/yuno/v4/working_hours/instructor/${e.id}`,learnersV2:`/wp-json/yuno/v2/${e.role}/mylearners/${e.instructorID}/${e.view}/${e.limit}/${e.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private",freebusy:"/wp-json/yuno/v2/instructor/freebusy/batch",learnersV2:`/wp-json/yuno/v2/${e.role}/mylearners/${e.instructorID}/${e.view}/${e.limit}/${e.offset}`}[t]||"")},attendance:function(t,e,n,o,a){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${e}/${n}/${o}/${a}`}[t]||"")},user:function(t,e){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${e.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${e.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${e.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${e.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots",slotsV4:`/wp-json/yuno/v4/availability/free_slots/${e.params}`}[t]||"")},classInsights:function(t,e,n,o,a){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${e}/${n}/${o}/${a}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${e}/${n}/${o}/${a}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${e}/${n}/${o}/${a}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${e}/${n}/${o}/${a}`}[t]||"")},resource:function(t,e){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${e.role}/${e.userID}/${e.limit}/${e.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${e.batchID}/${e.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[t]||"")},academy:function(t,e){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",fetchAcademiesFilters:`/wp-json/yuno/v4/academies/filters${e.params}`,academiesV2:`/wp-json/yuno/v4/academies/${e.view_type}${e.params}`,activeOrg:"/wp-json/yuno/v3/user/state",getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${e.id}/${e.org_id}/${e.academy_id}/${e.days}/${e.status}/${e.vc_status}/${e.course_id}/${e.category_id}/${e.is_featured}/${e.native_language}/${e.avg_rating}/${e.view_type}/${e.limit}/${e.offset}`,addDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/add",getDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/${e.id}/${e.org_id}/${e.academy_id}`,updateDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/edit",fetchAcademyDetails:`/wp-json/yuno/v4/academies/${e.id}`,organizations:`/wp-json/yuno/v4/organizations/${e.org_id}`,fetchAllPlaces:`/wp-json/yuno/v4/places/${e.view_type}?org_id=${e.org_id}&limit=${e.limit}&offset=${e.offset}`,fetchPlace:`/wp-json/yuno/v4/places/${e.placeID}`}[t]||"")},googleMapLocation:function(t,e){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${e.latitude},${e.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${e.key}`}[t]||"")},google:function(t,e){return"https://www.google.com"+({openLocation:`/maps/search/?api=1&query=Google&query_place_id=${e.placeID}`}[t]||"")},Places:function(t,e){return this.pickHost()+({create:"/wp-json/yuno/v4/places",fetchAllPlaces:`/wp-json/yuno/v4/places/${e.view_type}`,fetchPlace:`/wp-json/yuno/v4/places/${e.placeID}`,fetchMapPlaceDetails:`/wp-json/yuno/v4/maps/places/details?place_id=${e.placeID}`}[t]||"")},classroom:function(t,e){return this.pickHost()+({createClassroom:"/wp-json/yuno/v4/classrooms"}[t]||"")},createCourse:function(t,e){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules",status:`/api/status/${e.loggedinUserID}/all/${e.jobID}`}[t]||"")},enrollmentsV4:function(t,e){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${e.view}/${e.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${e.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${e.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${e.enrollmentID}/unenroll`}[t]||"")},header:function(t,e){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${e.userID}/${e.orgID}`}[t]||"")},payment:function(t,e){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${e.view}/${e.params}`,filters:`/wp-json/yuno/v4/payments/filters/${e.params}`}[t]||"")},batch:function(t,e){return this.pickHost()+({learners:`/wp-json/yuno/v4/enrollments/active/batch/${e.batchID}`}[t]||"")},courseV4:function(t,e){return this.pickHost()+({list:"/wp-json/yuno/v4/courses/search"}[t]||"")}},n=new Promise((function(t,e){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){t(!0)},n.onsuccess=function(){t(!1)}}else t(null)}catch(e){console.log(e),t(null)}})),o=function(){return performance.now()};return{config:e,findObjectByKey:(t,e,n)=>t.find((t=>t[e]===n))||null,heightOfEle:function(t,e){let n=t.offsetHeight;if(e){let e=getComputedStyle(t);return n+=parseInt(e.marginTop)+parseInt(e.marginBottom),n}return n},assignVValidationObj:function(t){const e=window.VeeValidate,n=window.VeeValidateRules,o=e.ValidationProvider,a=e.ValidationObserver;e.extend("minLength",{validate:(t,{length:e})=>t.length>=e,params:["length"],message:"At least {length} items must be selected"}),e.extend("maxLength",{validate:(t,{length:e})=>t.length<=e,params:["length"],message:"No more than {length} items must be selected"}),e.extend("isSelected",{validate:(t,{length:e})=>0!==e,params:["length"],message:"Custom title is not allowed"}),e.extend("isSelectedFromList",{validate:(t,{length:e})=>0!==e,params:["length"],message:"Please select the user from list"}),e.extend("isBatchSelected",{validate:(t,{length:e})=>0!==e,params:["length"],message:"Please select the batch from list"}),e.extend("notAllowed",{validate:(t,{number:e})=>!1===/^0[0-9].*$/.test(t),params:["number"],message:"Phone number can't start with {number}"}),e.extend("greaterThen",{validate:(t,{number:e})=>t>e,params:["number"],message:"Value should be greater then {number}"}),e.extend("isOverlapping",{validate:t=>!t,message:"Time overlap with another set of time"}),e.extend("isEndTime",{validate:t=>!t,message:"Choose an end time later than the start time."}),e.extend("selectLearner",{validate:(t,{number:e})=>0!==e,params:["number"],message:"Please add at least 1 learner from list"}),e.extend("isEmpty",{validate:(t,{getValue:e})=>""!==e,params:["getValue"],message:"Field should not be blank"}),e.extend("isNotBlank",{validate:(t,{getValue:e})=>null!==e,params:["getValue"],message:"Please select the learner from list"}),e.extend("url",{validate:(t,{getValue:e})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(t),params:["getValue"],message:"Please enter valid URL"}),e.extend("httpsURL",{validate:(t,{getValue:e})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(t),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),e.extend("email",{validate:t=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t),message:"Please enter a valid email address"}),e.extend("hasCurlyBrackets",{validate:t=>/\{.+?\}/.test(t),message:"String must have curly brackets with content inside"});for(let o in t.messages)e.extend(o,n[o]);e.localize("validationMsg",t),Vue.component("ValidationProvider",o),Vue.component("ValidationObserver",a)},removeObjInArr:function(t,e,n){let o=t.length;for(;o--;)t[o]&&t[o].hasOwnProperty(e)&&arguments.length>2&&t[o][e]===n&&t.splice(o,1);return t},formatDate:function(t){var e=new Date(t);if(isNaN(e.getTime()))return t;return day=e.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][e.getMonth()]+" "+day+" "+e.getFullYear()},dateTimeToArray:function(t){new Array;return t.split(" ")},timeConvert:function(t){return(t=t.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[t]).length>1&&((t=t.slice(1))[5]=+t[0]<12?"AM":"PM",t[0]=+t[0]%12||12),t.join("")},getQueryParameter:function(t){for(var e=window.location.search.substring(1).split("&"),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t)return o[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(t){n.then((function(e){t(e)}))},setCookie:function(t,e,n){let o=new Date;void 0===n&&(n=30),o.setTime(o.getTime()+24*n*60*60*1e3);let a="expires="+o.toGMTString();document.cookie=t+"="+e+";"+a+";path=/"},deleteCookie:function(t){document.cookie=t+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(t){let e=("; "+document.cookie).split("; "+t+"=");if(2==e.length)return e.pop().split(";").shift()},timestamp:o,removeValInArr:function(t){let e,n,o=arguments,a=o.length;for(;a>1&&t.length;)for(e=o[--a];-1!==(n=t.indexOf(e));)t.splice(n,1);return t},hasInArray:function(t,e){return-1!=t.indexOf(e)},getFromString:function(t,e,n){let o=t.match(e);return null!=o&&(!0===n?o[1].replace(/\/$/,""):o[1])},encodeObj:function(t){return encodeURI(JSON.stringify(t))},detectQueryString:function(){const t=window.location.search;return t||!1},scrollToElement:function(t,e,n){let o=window.pageYOffset,a=(r=t,window.pageYOffset+document.querySelector(r).getBoundingClientRect().top);var r;targetY=document.body.scrollHeight-a<window.innerHeight?document.body.scrollHeight-window.innerHeight:a,customHeight=void 0!==n?n:74,diff=targetY-o-customHeight;let i="";diff&&window.requestAnimationFrame((function t(n){i||(i=n);let a=n-i,r=Math.min(a/e,1);var s;r=(s=r)<.5?4*s*s*s:(s-1)*(2*s-2)*(2*s-2)+1,window.scrollTo(0,o+diff*r),a<e&&window.requestAnimationFrame(t)}))},removeTagsFromString:function(t){return t.replace(/(<([^>]+)>)/gi,"")},findInArray:function(t,e){return void 0!==t.find((t=>t===e))},queryParameterNonWindow:function(t,e){for(var n=t.substring(1).split("&"),o=0;o<n.length;o++){var a=n[o].split("=");if(a[0]==e)return a[1]}return!1},cleanTextAndTruncate:function(t,e){let n=t.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>e){const t=n.lastIndexOf(" ",e-1);return n.substring(0,t)+"..."}return n}}}(jQuery),YUNOStore=function(t){const e=function(t,e,n,o,a){if(o){if(void 0===e.addToModule||e.addToModule)if(void 0!==a&&a){for(let e=0;e<n.length;e++)t.data.push(n[e]);e.hasLoadmore&&(t.count=e.response.data.count,t.currentCount=t.data.length,t.offset=t.currentCount)}else t.data=n}else e.moduleTabs?(t.error=n,t.loading=!1):t.error=n;t.success=!0,t.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[],isMenuOpen:!1},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{ClassTitle:"",ClassDescription:"",ClassDate:"",ClassTime:"",ClassDuration:"",classSchedule:"",learner:[],RelatedCourses:"",BatchID:"",academy_id:""}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],calendarLoading:!1},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(t,n){if(n.isError){let o=n.response.response,a=!(void 0===n.pushData||!n.pushData),r=t[n.store],i="";i=void 0!==o&&void 0!==o.data&&void 0!==o.data.message?o.data.message:YUNOCommon.config.errorMsg.common,console.log(i),console.log(n.store),n.tabs?(e(r.tabs[n.tabIndex],n,o,!1,a),r.tabs[n.tabIndex].error=!0,r.tabs[n.tabIndex].errorData=i,n.callback&&n.callbackFunc(r.tabs[n.tabIndex].errorData)):(e(r,n,o,!1,a),r.error=!0,r.errorData=i,n.callback&&n.callbackFunc(r.errorData))}else{let o=n.response.data.data,a=!(void 0===n.pushData||!n.pushData),r=n.response.data,i=t[n.store];n.tabs?(204===r.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=r.message,console.log(r.message),console.log(n.store)),e(i.tabs[n.tabIndex],n,o,!0,a)):(204===r.code&&(i.error=!0,i.errorData=r.message,console.log(r.message),console.log(n.store)),401===r.code&&(i.error=!0,i.errorData=r.message,console.log(r.message),console.log(n.store)),e(i,n,o,!0,a)),n.callback&&n.callbackFunc(n)}},thirdParty(t,e){module=t[e.store],module.error?e.callbackFunc(module):e.callbackFunc(e)},mapCourses(t,n){if(n.isError){let o=n.response;(void 0===n.overrideData||n.overrideData)&&e(t[n.module],n,o,!1),n.callback&&n.callbackFunc(t[n.module].errorData)}else{let o=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&e(t[n.module],n,o,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(t,n){if(n.isError){let o=n.response;e(t[n.module],n,o,!1)}else{let o=n.response.data.data;n.isTabAdded||t.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),e(t[n.module],n,o,!0),n.isTabAdded=!0,setTimeout((()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(t,n){if(n.isError){let o=n.response;e(t[n.module],n,o,!1)}else{let a=n.response.data.data;for(var o=0;o<a.length;o++)a[o].username_email_phone=`${a[o].username_email} (${a[o].phone})`;e(t[n.module],n,a,!0)}},classDelete(t,e){if(e.isError)t[e.module].error=e.response,t[e.module].success=!0,t[e.module].loading=!1,e.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(t.loader.isActive=!1,t.loader.overlay=!1,void 0!==e.classID){let n=t.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",e.classID),0===n.length&&(t.instructorHome.tabs[0].error=!0),e.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else e.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});t[e.module].data=e.response,t[e.module].success=!0,t[e.module].loading=!1}},demoClassEnroll(t,e){if(e.isError)t[e.module].isLoading=!1,void 0!==e.classIndex&&(t.loader.isActive=!1,t.loader.overlay=!1),e.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),t[e.module].isLoading=!1,t[e.module].data=e.response.data.data,void 0!==e.classIndex){let n=t[e.parentModule].data[e.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),t.loader.isActive=!1,t.loader.overlay=!1}else t[e.parentModule].data.is_enrolled=!0;e.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(t,e){e.isError?(t[e.module].error=e.response,t[e.module].success=!0,t[e.module].loading=!1):(t[e.module].data=e.response.data.data,t[e.module].response=e.response.data,t[e.module].success=!0,t[e.module].loading=!1,""!==e.componentInstance&&e.componentInstance.getUserRole(e.response.data.data))},userProfile(t,e){e.isError?(t[e.module].error=e.response,t[e.module].success=!0,t[e.module].loading=!1):(t[e.module].data=e.response.data.data,t[e.module].success=!0,t[e.module].loading=!1,""!==e.instance&&void 0!==e.instance.gotUserProfile&&e.instance.gotUserProfile(e.response.data.data))},classDetail(t,n){if(n.isError){let o=n.response;"enrolled"===n.nestedTab?(t[n.module].tabs[n.tabIndex].loading=!1,t[n.module].tabs[n.tabIndex].pageLoading=!1,t[n.module].tabs[n.tabIndex].error=o):e(t[n.module],n,o,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(t,e){if(e.isError){if(e.batches){t[e.module].data[e.courseIndex].tabs[e.tabIndex].error=e.response}else t[e.module].error=e.response;t[e.module].success=!0,t[e.module].loading=!1}else{let n=e.response.data.data;if(e.batches){t[e.module].data[e.courseIndex].isBatches=!0;for(let o=0;o<n.length;o++)t[e.module].data[e.courseIndex].tabs[e.tabIndex].data.push(n[o]);let o=t[e.module].data[e.courseIndex].tabs[e.tabIndex];o.count=e.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount,o.isLoadMore=!1}else{for(let t=0;t<n.length;t++)n[t].isBatches=!1,n[t].isLoading=!1,n[t].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];t[e.module].data=n}t[e.module].success=!0,t[e.module].loading=!1}},allLearners(t,e){if(e.isError)t[e.module].error=e.response,t[e.module].success=!0,t[e.module].loading=!1,void 0!==e.nested&&(t[e.nested].tabs[0].error=!0);else{let o=e.response.data.data;if(void 0!==e.nested){let a=o.columns,r=o.rows,i={field:"actions",label:"Actions",sortable:!1};"Instructor"===e.userRole&&a.push(i);for(var n=0;n<r.length;n++)r[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+r[n].id};t[e.nested].tabs[0].data=o,t[e.nested].tabs[0].totalResult=e.response.data.count,t[e.nested].tabs[0].pageLoading=!1}t[e.module].data=o.rows,t[e.module].success=!0,t[e.module].loading=!1}},instructorHome(t,e){const n=function(n){n?(t[e.module].tabs[e.index].hasData=!0,t[e.module].tabs[e.index].data=e.response.data.data):(t[e.module].tabs[e.index].hasData=!1,t[e.module].tabs[e.index].data=e.response,t[e.module].tabs[e.index].error=!0),t[e.module].success=!0,t[e.module].loading=!1,t[e.module].tabs[e.index].success=!0,t[e.module].tabs[e.index].loading=!1};e.isError?n(!1):n(!0)},instructorLearners(t,e){const n=function(n){if(n&&void 0!==e.response.data){let n=e.response.data.data;if(void 0!==e.form){if(t[e.module][e.form].data=n,t[e.module][e.form].modal=!1,"newGroupModal"===e.form){let o={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:e.payload.title,total_users:0,user:e.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};t[e.module].tabs[e.index].data.unshift(o),e.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===e.form){t[e.module].tabs[e.index].data.filter((function(t){return t.group_id===e.group.group_id}))[0].user=e.group.user;let n=t.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],e.componentInstance.learnerAdded=!0,e.componentInstance.group=null,e.componentInstance.learnersList=[],e.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===e.form&&e.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const a=t[e.module].tabs[e.index];a.hasData=!0,a.isLoadMore=!1;for(var o=0;o<n.length;o++)n[o].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[o].group_created_time)[0]),n[o].scheduleClassURL=`/class-schedule/?groupID=${n[o].group_id}`,a.data.push(n[o]);a.count=e.response.data.count,a.currentCount=a.data.length,a.offset=a.currentCount}}else void 0!==e.form?(t[e.module][e.form].modal=!1,e.componentInstance.$buefy.toast.open({duration:5e3,message:`${e.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(t[e.module].tabs[e.index].hasData=!1,t[e.module].tabs[e.index].data=e.response,t[e.module].tabs[e.index].error=!0,t[e.module].tabs[e.index].errorData=e.response);void 0!==e.form?t[e.module][e.form].isLoading=!1:(t[e.module].success=!0,t[e.module].loading=!1,t[e.module].tabs[e.index].success=!0,t[e.module].tabs[e.index].loading=!1)};e.isError?n(!1):(n(!0),204===e.response.data.code&&(t[e.module].tabs[e.index].error=!0,t[e.module].tabs[e.index].errorData=e.response.data.message,console.log(e.response.data.message)))}},actions:{fetchThirdPartyData({commit:t,state:e},n){let o=e[n.store];o.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then((e=>{o.loading=!1,n.response=e,o.error=null,t(n.module,n)})).catch((e=>{o.loading=!1,o.errorData=e,n.response=e,o.error=!0,t(n.module,n)}))},postThirdPartyData({commit:t,state:e},n){let o=e[n.store];o.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then((e=>{o.loading=!1,n.response=e,o.error=null,t(n.module,n)})).catch((e=>{o.loading=!1,o.errorData=e,n.response=e,o.error=!0,t(n.module,n)})):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then((e=>{o.loading=!1,n.response=e,o.error=null,t(n.module,n)})).catch((e=>{o.loading=!1,o.errorData=e,n.response=e,o.error=!0,t(n.module,n)})):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then((e=>{o.loading=!1,n.response=e,o.error=null,t(n.module,n)})).catch((e=>{o.loading=!1,o.errorData=e,n.response=e,o.error=!0,t(n.module,n)})):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then((e=>{o.loading=!1,n.response=e,o.error=null,t(n.module,n)})).catch((e=>{o.loading=!1,o.errorData=e,n.response=e,o.error=!0,t(n.module,n)})):console.log("not defined")},fetchData({commit:t,state:e},n){let o="",a="";a="0"!==isLoggedIn?{authorization:e.config.yunoAPIToken}:{authorization:""},o=void 0!==n.store?n.tabs?e[n.store].tabs[n.tabIndex]:e[n.store]:e[n.module],void 0===n.moduleLoading||n.moduleLoading?o.loading=!0:o.loading=!1,n.moduleTabs&&n.isTabLoader&&(o.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:a}).then((o=>{n.response=o,n.isError=!1,t(n.module,n),403===(n.response?.data?.data?.status??"")&&(e.config.unauthorizedModal||("userInfo"===n.store&&(e.header.success=!0,e.footer.success=!0,e.capabilities.success=!0),e.config.unauthorizedModal=!0))})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0,e[n.store].loading=!1,e[n.store].success=!0;let a="";a=void 0!==n.store?n.tabs?e[n.store].tabs[n.tabIndex]:e[n.store]:e[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),403===o.response.data.data.status&&(e.config.unauthorizedModal||("userInfo"===n.store&&(e.header.success=!0,e.footer.success=!0,e.capabilities.success=!0),e.config.unauthorizedModal=!0)),n.response=o,n.isError=!0,t(n.module,n)}))},putData({commit:t,state:e},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=e.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=e.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:o}).then((e=>{n.response=e,n.isError=!1,t(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0;let a="";a=void 0!==n.store?e[n.store]:e[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,t(n.module,n)}))},awsPutData({commit:t,state:e},n){let o={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?o.authorization=e.config.yunoAPIToken:o.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((e=>{n.response=e,n.isError=!1,t(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0,n.response=o,n.isError=!0,t(n.module,n)}))},postData({commit:t,state:e},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=e.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=e.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:o}).then((e=>{n.response=e,n.isError=!1,t(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0;let a="";a=void 0!==n.store?e[n.store]:e[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,t(n.module,n)}))},awsPostData({commit:t,state:e},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=e.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?o.authorization=e.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((e=>{n.response=e,n.isError=!1,t(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0;let a="";a=void 0!==n.store?e[n.store]:e[n.module],void 0!==a.errorData&&void 0!==o.response&&(a.errorData=o.response),n.response=o,n.isError=!0,t(n.module,n)}))},deleteData({commit:t,state:e},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=e.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=e.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:o,data:n.payload}).then((e=>{n.response=e,n.isError=!1,t(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),e[n.store].error=!0,n.response=o,n.isError=!0,t(n.module,n)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const t=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+t+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const t=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+t+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:t=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(t,e){Event.$emit("emptyStateCTA",t,e)},setPayload(){let t=this.signIn,e="";e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",t.categoryURL=`/${e}`,t.landing_page.url=window.location.origin+window.location.pathname,t.landing_page.title=document.title,t.productCode="",t.leadStatus="",t.utmSource=YUNOCommon.getQueryParameter("utm_source"),t.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),t.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),t.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),t.adContent=YUNOCommon.getQueryParameter("ad_content"),t.utmTerm=YUNOCommon.getQueryParameter("utm_term"),t.gclid=YUNOCommon.getQueryParameter("gclid"),t.content.type="",t.content.id=""},initGoogleSignIn(t){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",t)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOHeader=function(t){const e=_.debounce(((t,e)=>{t.categories.isLoading=!0,t.categories.data=[],t.fetchCourseSuggestions(e)}),700);return{header:function(){Vue.component("yuno-header",{props:["isnav","scrollenabled","logoAlignment","postsignup","hassearchbar","options"],template:'\n                <div>\n                    <header id="yunoHeader" class="yunoHeader" :class="{\'noNav\': isnav === false, \'scrollEnabled\': scrollenabled, \'logoCenter\': logoAlignment}">\n                        <div class="container-fluid noOverflow">\n                            <nav class="navbar navbar-expand-lg" :class="[hassearchbar !== undefined && hassearchbar.isActive ? \'hasSearchBar\' : \'\',]">\n                                <figure class="logo navbar-brand">\n                                    <a :href="isnav !== false ? getHomeURL : \'#\'"><img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                </figure>\n                                <button v-if="isnav !== false" class="navbar-toggler" type="button" aria-controls="navbarSupportedContent" :aria-expanded="enabledSubmenu" aria-label="Toggle navigation" @click="toggleMenu">\n                                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                </button>\n                                <div v-if="isnav !== undefined && !isnav" class="yunoLogin">\n                                    <div v-if="false" class="yunoCallUs noSpacer" :class="{\'preLogin\': getUserStatus !== true}">\n                                        <a class="whatsapp" href="https://api.whatsapp.com/send?phone=+918847251466" target="_blank"><i class="fa fa-whatsapp" aria-hidden="true"></i></a>\n                                    </div>        \n                                </div>\n                                <div v-if="isnav !== false" class="collapse navbar-collapse yunoMainNav" id="yunoMainNav" :class="[!getUserStatus || getUserStatus && userRole.data === \'Learner\' ? \'hasSearch\' : \'\', userRole.data === \'yuno-admin\' ? \'hasAdmin\' : \'\', enabledSubmenu ? \'show\' : \'collapsed\']">\n                                    <ul class="navbar-nav align-items-center w-100">\n                                        <template v-if="headerMenu.loading" v-for="(menu, menuIndex) in 3">\n                                            <li :style="{ marginLeft: \'30px\' }">\n                                                <b-skeleton width="100px" height="32px" active></b-skeleton>\n                                            </li>\n                                        </template>\n                                        <template v-if="headerMenu.success" v-for="(menu, menuIndex) in headerMenu.data">\n                                            <li class="hasCTA" v-if="detectPlatform() === \'android\'">\n                                                <b-button tag="a"\n                                                    href="https://play.google.com/store/apps/details?id=com.yunolearning.learn"\n                                                    target="_blank"\n                                                    class="yunoPrimaryCTA small">\n                                                    Get the app\n                                                </b-button>\n                                            </li>\n                                            <template v-if="menu.type === \'menu_item\'">\n                                                <li \n                                                    class="nav-item" \n                                                    :class="{\'active\': menu.is_active === true, \'dropdown\': menu.items.length, \'isCTA\': menu.section === \'Invite\'}" \n                                                    :key="menuIndex">\n                                                    <template v-if="menu.items.length">\n                                                        <a \n                                                            :id="\'submenu\' + menuIndex"\n                                                            :class="{\'dropdownToggle\': menu.items.length}" \n                                                            @click="manageSubmenu($event, menu, \'submenu\' + menuIndex)"\n                                                            :ref="\'submenu\' + menuIndex"\n                                                            :href="menu.url">\n                                                            {{menu.section}}\n                                                        </a>\n                                                    </template>\n                                                    <a \n                                                        v-else\n                                                        :class="{\'dropdown-toggle\': menu.items.length}" \n                                                        @click="manageCustomLink($event, menu)"\n                                                        :href="menu.url">\n                                                            {{menu.section}}\n                                                    </a>\n                                                </li>\n                                            </template>\n                                            <template v-if="menu.type === \'search_item\'">\n                                                <li class="hasSearchBar">\n                                                    <div class="searchBarWrapper">\n                                                        <validation-observer \n                                                            tag="div" \n                                                            ref="searchObserver" \n                                                            v-slot="{ handleSubmit, invalid }">\n                                                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                                                <b-field class="searchFieldWrapper">\n                                                                    <validation-provider \n                                                                        tag="div"\n                                                                        class="searchField"\n                                                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                                                        v-slot="{ errors, classes }">\n                                                                        <b-autocomplete\n                                                                            :class="classes"\n                                                                            v-model="categories.current"\n                                                                            :data="categories.data"\n                                                                            autocomplete="courseSearch"\n                                                                            :loading="categories.isLoading"\n                                                                            placeholder="Search..."\n                                                                            @typing="searchOnTyping"\n                                                                            @select="onSelect($event)"\n                                                                            :clearable="true"\n                                                                        >\n                                                                            <template slot-scope="props">\n                                                                                <template v-if="props.option.course_url">\n                                                                                    <div class="suggestion courseBlock">\n                                                                                        <figure>\n                                                                                            <div class="imageWrapper" v-if="false">\n                                                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                                                            </div>\n                                                                                            <figcaption>\n                                                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                                                <p class="courseDetail">\n                                                                                                    <span class="caption">Course</span>\n                                                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                                                </p>\n                                                                                            </figcaption>\n                                                                                        </figure>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                            </template>\n                                                                        </b-autocomplete>\n                                                                    </validation-provider>\n                                                                    <div class="ctaWrapper">\n                                                                        <b-button\n                                                                            native-type="submit"\n                                                                            class="doSearch">\n                                                                            <span class="material-icons-outlined">search</span>\n                                                                        </b-button>  \n                                                                    </div>\n                                                                </b-field>\n                                                            </form>\n                                                        </validation-observer>\n                                                    </div>\n                                                </li> \n                                            </template>\n                                        </template>\n                                    </ul>\n                                </div>\n                                <template v-if="isnav !== false">\n                                    <div class="yunoLogin" v-if="!getUserStatus">\n                                        <a \n                                            class="marginRight15"\n                                            @click="trackLoginLandingPage()"\n                                            href="/login/">\n                                            <span class="yuno-login-with-google-on-pages grey wired">Log in</span>\n                                        </a>\n                                        <a @click="trackLoginLandingPage()" href="/login/?type=signup">\n                                            <span class="yuno-login-with-google-on-pages">Sign up</span>\n                                        </a>\n                                        <div class="yunoCallUs" v-if="false" :class="{\'preLogin\': getUserStatus !== true}">\n                                            <template v-if="false">\n                                                <span class="caption">Call us at</span>\n                                                <a href="tel:+91-8847251466"><span class="material-icons">call</span> <span class="value">+91-8847251466</span></a>\n                                            </template>\n                                            <a href="#" @click.prevent="bookADemo()">Book a demo class</a>\n                                        </div>        \n                                    </div>\n                                </template>\n                                <template v-if="isnav !== false || hassearchbar !== undefined && hassearchbar.isActive">\n                                    <template v-if="getUserStatus">\n                                        <div class="dropdown yunoLoginDropdown" v-if="getUserProfile.success">\n                                            <a class="dropdown-toggle" href="#" role="button" id="userProfile" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                                                <figure class="profilePic">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.first_name">\n                                                </figure>\n                                            </a>\n                                            <div class="dropdown-menu" aria-labelledby="userProfile">\n                                                <figure class="menuHeader">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.yuno_display_name">\n                                                    <figcaption>\n                                                        <p class="userName">{{ getUserProfile.data.yuno_display_name }}</p>\n                                                        <p class="userEmail">{{ getUserProfile.data.email }}</p>\n                                                    </figcaption>\n                                                </figure>\n                                                <ul class="userlinks">\n                                                    <li \n                                                        v-for="(item, l) in usermenu"\n                                                        :key="l"\n                                                        v-if="item.isActive"\n                                                        :class="[item.slug === \'switchAccount\' ? \'linksFooter\' : \'\']">\n                                                        <a \n                                                            v-if="item.callbackFunc !== false" \n                                                            class="dropdown-item" \n                                                            @click="item.callbackFunc" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                        <a \n                                                            v-else \n                                                            class="dropdown-item" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                    </li>\n                                                </ul>\n                                                <div class="additionalItems" v-if="isItemAvailable([\'Instructor\'])">\n                                                    <template v-if="referralCode.loading">\n                                                        <div class="loaderWrapper">\n                                                            <div class="smallLoader"></div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-if="referralCode.success">\n                                                        <template v-if="referralCode.error === null">\n                                                            <div class="item">\n                                                                <b-field label="Referral code">\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="material-icons-outlined">content_copy</span>\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </template>\n                                </template>\n                            </nav>\n                        </div>\n                        <b-modal \n                            :active.sync="invite.modal" \n                            :width="500" \n                            :can-cancel="[\'escape\', \'x\']"\n                            class="yunoModal inviteModal">\n                                <div class="modalHeader">\n                                    <h2 class="modalTitle">Invite Link</h2>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="wrapper">\n                                        <template v-if="inviteLink.loading">\n                                            <div class="loaderWrapper">\n                                                <div class="smallLoader"></div>\n                                            </div>\n                                        </template>\n                                        <template v-if="inviteLink.success">\n                                            <template v-if="inviteLink.error === null">\n                                                <ul class="classFields">\n                                                    <li>\n                                                        <div class="clipboard">\n                                                            <b-input id="inviteLink" :value="inviteLink.data.invitation_link" readonly></b-input>\n                                                            <i @click="copyToClipboard(\'inviteLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n                                                        </div>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else>\n                                                <p>{{inviteLink.error}}</p>\n                                            </template>\n                                        </template>\n                                    </div>\n                                </div>\n                        </b-modal>\n                    </header>\n                    <div class="yunoSubmenu" \n                        :class="[submenu.isActive ? \'active\' : \'inActive\']"\n                        v-if="submenu.isActive && scrollenabled === undefined || submenu.isActive && scrollenabled !== undefined && !scrollenabled" \n                        v-closable="{exclude: [submenu.ref], handler: \'onClose\'}">\n                        <div class="container">\n                            <a href="#" class="closeSubmenu" @click.prevent="closeSubmenu()">\n                                <span class="material-icons">arrow_back_ios</span>\n                                {{ selectedSubmenu }}\n                            </a>\n                            <ul class="submenuList" :class="[submenu.data.items.length < 3 ? \'col2\' : \'\']">\n                                <li v-for="(nav, i) in exploreMenu" :key="i" v-if="nav.isVisible">\n                                    <p :class="[selectedSubmenu === nav.label ? \'active\' : \'\']">\n                                        <span class="material-icons-outlined">\n                                            {{ nav.icon }}\n                                        </span>\n                                        {{ nav.label }}\n                                        <span @click="manageSubmenuList(nav)" class="material-icons-outlined chevron">chevron_right</span>\n                                    </p>\n                                    <ul class="itemsList" :class="[nav.isItemsActive ? \'active\' : \'\']">\n                                        <li v-for="(item, j) in nav.items" :key="j">\n                                            <a :href="item.url">\n                                                {{ item.label }}\n                                            </a>\n                                        </li>\n                                    </ul>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    <yuno-choose-account-type>\n                    </yuno-choose-account-type>\n                    <b-modal \n                        :active.sync="bookademo.modal" \n                        :width="388" \n                        :can-cancel="[\'escape\', \'x\']"\n                        :on-cancel="bookademoModalClose"\n                        class="yunoModal lightTheme">\n                            <template v-if="bookademo.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">\n                                        {{ bookademo.title }}\n                                    </h3>\n                                </div>\n                                <div class="modalBody">\n                                    <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observerWrapper"\n                                        ref="bookademoobserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form @submit.prevent="handleSubmit(initBookADemo)">\n                                            <validation-provider \n                                                tag="div" \n                                                class="categoryWithImage" \n                                                :rules="{required:true}" \n                                                v-slot="{ errors, classes }"\n                                            >\n                                                <template v-for="(option, i) in bookademo.data">\n                                                    <div class="fieldWrapper">\n                                                        <div class="inner">\n                                                            <b-field :key="i" :style="categoryImg(option)">\n                                                                <b-radio-button \n                                                                    :class="classes"\n                                                                    v-model="bookademo.selected"\n                                                                    @input="initBookADemo()"\n                                                                    name="bookademo"\n                                                                    :native-value="option">\n                                                                    {{option.label}}\n                                                                </b-radio-button>\n                                                            </b-field>\n                                                            <div class="catLabel">{{option.label}}</div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>\n                                            <div class="ctaWrapper alignLeft" v-if="false">\n                                                <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>\n                                            </div>\n                                        </form>\n                                    </validation-observer>\n                                </div>\n                            </template>\n                    </b-modal>\n                </div>\n            ',data(){return{enabledSubmenu:!1,bookademo:{modal:!1,title:"Book a Demo Class",subtitle:"Choose Subject Category",selected:"",data:[]},showSubmenu:!1,invite:{modal:!1},submenu:{isActive:!1,data:[],ref:""},usermenu:[{label:"Home",slug:"learnerDashboard",icon:"home",iconType:"material-icons-outlined",url:"/learner/",isActive:!1,callbackFunc:!1},{label:"Bookmarks",slug:"myBookmarks",icon:"bookmark_border",iconType:"material-icons-outlined",url:"/my-bookmarks/",isActive:!1,callbackFunc:!1},{label:"Enrolled Courses",slug:"myEnrollments",icon:"local_library",iconType:"material-icons-outlined",url:"/learner-courses/",isActive:!1,callbackFunc:!1},{label:"Subscribed Categories",slug:"categories",icon:"category",iconType:"material-icons-outlined",url:"/subscribed-categories",isActive:!1,callbackFunc:!1},{label:"My Profile",slug:"myProfile",icon:"account_circle",iconType:"material-icons-outlined",url:"",isActive:!1,callbackFunc:!1},{label:"Settings",slug:"settings",icon:"settings",iconType:"material-icons-outlined",url:"/settings/",isActive:!0,callbackFunc:!1},{label:"Switch account",slug:"switchAccount",icon:"swap_horiz",iconType:"material-icons-outlined",url:this.$store.getters.getSwitchAccountURL,isActive:!0,callbackFunc:()=>this.chooseAccountState()},{label:"Logout",slug:"logout",icon:"logout",iconType:"material-icons-outlined",url:"/logout/",isActive:!0,callbackFunc:!1}],errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[],level3Menus:[],selectedSubmenu:null,exploreMenu:[{label:"Test Preparation",icon:"assignment_turned_in",isItemsActive:!1,isVisible:!0,items:[{label:"IELTS",url:"/ielts/courses/"},{label:"TOEFL",url:"/toefl/courses/"},{label:"SAT",url:"/sat/courses/"},{label:"GRE",url:"/gre/courses/"},{label:"GMAT",url:"/gmat/courses/"},{label:"CUET",url:"/cuet/courses/"},{label:"NEET",url:"/neet/courses/"},{label:"JEE",url:"/jee/courses/"},{label:"PTE",url:"/pte/courses/"},{label:"Duolingo",url:"/duolingo/courses/"}]},{label:"Languages",icon:"language",isItemsActive:!1,isVisible:!0,items:[{label:"English Speaking",url:"/english-speaking/courses/"},{label:"Hindi",url:"/hindi/courses/"},{label:"French",url:"/french/courses/"},{label:"German",url:"/german/courses/"},{label:"Spanish",url:"/spanish/courses/"},{label:"Italian",url:"/italian/courses/"}]},{label:"Professional Skills",icon:"work_outline",isItemsActive:!1,isVisible:!0,items:[{label:"Soft Skills",url:"/soft-skills/courses/"},{label:"Microsoft Excel",url:"/microsoft-excel/courses/"},{label:"Data Science and Analytics",url:"/data-science-and-analytics/courses/"},{label:"Artificial Intelligence (AI)",url:"/artificial-intelligence/courses/"},{label:"Design",url:"/design/courses/"},{label:"Anil Lamba on Finance",url:"/anil-lamba-on-finance/courses/"}]},{label:"Learning for Kids",icon:"lightbulb",isItemsActive:!1,isVisible:!0,items:[{label:"Vedic Maths",url:"/vedic-maths/courses/"},{label:"Coding for Kids",url:"/coding-for-kids/courses/"},{label:"Python Programming",url:"/python-programming/courses/"}]}]}},computed:{...Vuex.mapState(["loginWithGoogle","user","userRole","inviteLink","module","referralCode","searchSuggestions","chooseAccountTypeModal","header"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},getSignInURL(){return this.$store.getters.getSignInURL},switchAccountURL(){return this.$store.getters.getSwitchAccountURL},headerMenu:{get(){return this.$store.state.header}},getUserStatus:{get:()=>0!==Number(isLoggedIn)},getUserProfile:{get(){return this.$store.state.userProfile}}},async created(){this.emitEvents()},mounted(){this.logoGoogleSchema(),this.$nextTick((()=>{this.cacheLevel3Menus()})),this.getUserStatus?this.getState():(["userState","oldUserState","paymentState","demoClassState","skipSignUp","userSignUpPage","userSignUp","noSignupRequired","isWelcomePage","isReferrer","isQuiz"].forEach((t=>localStorage.removeItem(t))),sessionStorage.removeItem("activeUserV1"))},methods:{manageSubmenuList(t){this.selectedSubmenu=t.label,t.isItemsActive=!t.isItemsActive;this.exploreMenu.forEach((e=>{e.label!==t.label&&(e.isVisible=!e.isVisible)}))},cacheLevel3Menus(){this.level3Menus=Array.from(document.querySelectorAll(".dropdown-menu.level3"))},toggleMenu(){this.enabledSubmenu=!this.enabledSubmenu,this.header.isMenuOpen=this.enabledSubmenu},manageCustomLink(t,e){"Teach on Yuno"===e.section&&(t.preventDefault(),this.chooseAccountTypeModal.modal=!0)},isObjectBlank:t=>"object"==typeof t&&null!==t&&!Array.isArray(t)&&0===Object.keys(t).length,getQueryParams(t){const e=new URLSearchParams(t.split("?")[1]);let n={};for(const[t,o]of e)n[t]=o;return n},trackLoginLandingPage(){if(!this.user.isLoggedin){let t="",e="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e=this.isObjectBlank(this.getQueryParams(window.location.origin+window.location.pathname+window.location.search))?null:this.getQueryParams(window.location.origin+window.location.pathname+window.location.search);const n={url:window.location.origin+window.location.pathname,pageTitle:document.title,category:t,urlParams:e,zohoMeta:this.$props.options.zohoMeta};sessionStorage.setItem("landingPage",JSON.stringify(n))}},categoryImg:t=>({"background-image":`url(${t.image.replace(/ /g,"%20")})`}),bookademoModalClose(){this.bookademo.selected="",this.bookademo.data=[]},initBookADemo(){window.location.href=this.bookademo.selected.url},bookADemo(){this.bookademo.modal=!0;let t=JSON.parse(JSON.stringify(YUNOCommon.findObjectByKey(this.headerMenu.data,"label","Explore").submenu));const e={ielts:"/for-ads/ielts/ielts-for-all-v3/","english speaking":"/for-ads/english-speaking/english-for-all-v3/",pte:"/for-ads/pte/pte-for-all-v3/",duolingo:"/for-ads/duolingo-for-all/",toefl:"/for-ads/toefl-for-all/",french:"/for-ads/french/french-for-all-v3/"};t.forEach((t=>{t.slug=t.label.toLowerCase(),e[t.slug]&&(t.url=e[t.slug])})),this.bookademo.data=t},isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},initForm(){const t=this.categories.selected,e={filter:"category",id:t.category_id,label:t.category,parent_id:0,slug:t.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",e,n)},onSelect(t){t&&(t.course_url?window.location.href=t.course_url:t.course_count&&(this.categories.selected=t,this.payload.search=t.id,this.searchParams.category=[t.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],t.parent_cat_slug&&(this.searchParams.category=[t.parent_cat_id],this.searchParams.category_level_1=[t.category_level_1],this.searchParams.category_level_2=[t.id]),this.initForm()))},gotCourseSuggestions(t){if(this.categories.isLoading=!1,200===t.response?.data?.code){const{course:e,category:n,sub_category:o}=t.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),e&&this.categories.data.push(...e)}},fetchCourseSuggestions(t){const e=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",t),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(t){return e.gotCourseSuggestions(t)}};this.$store.dispatch("fetchData",n)},searchOnTyping(t){t.length>2?e(this,t):this.categories.data=[]},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){t.response.data.data}},fetchReferralCode(){const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},emitEvents(){Event.$on("updateHeaderSearch",(()=>{})),Event.$on("checkLoggedInState",(t=>{t&&Event.$on("gotUserRole",((t,e)=>{"Learner"===t&&(this.$store.state.homeURL="/learner"),"Instructor"===t&&this.fetchReferralCode()}))}))},structuredData(t){const e=document.createElement("script");e.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(t));e.appendChild(n),document.head.appendChild(e)},logoGoogleSchema(){let t={"@context":"https://schema.org","@type":"Organization",url:"https://www.yunolearning.com/",logo:YUNOCommon.config.host()+"/wp-content/themes/yunolearning-child/assets/images/yuno.jpeg"};this.structuredData(t)},manageLogin(t,e){t.preventDefault(),Event.$emit("manageLogin",t,e)},detectPlatform:()=>/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":!!/Android/i.test(window.navigator.userAgent)&&"android",gotInviteLink(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code)this.inviteLink.data=t.response.data.data;else{const e=void 0!==t.response.data&&void 0!==t.response.data.message?t.response.data.message:YUNOCommon.config.errorMsg.common;this.inviteLink.error=e}},fetchInviteLink(){const t=this,e={apiURL:YUNOCommon.config.getInviteURLAPI(isLoggedIn),module:"gotData",store:"inviteLink",addToModule:!1,callback:!0,callbackFunc:function(e){return t.gotInviteLink(e)}};this.$store.dispatch("fetchData",e)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},initInviteModal(t){t.preventDefault(),this.invite.modal=!0,0===this.inviteLink.data.length&&this.fetchInviteLink()},whichPage:()=>window.location.href.indexOf("open")>-1?"open":null,submenuClasses(){this.level3Menus.length||this.cacheLevel3Menus(),this.level3Menus.forEach((t=>t.classList.remove("show")))},closeSubmenu(){if("Explore"===this.selectedSubmenu)this.onClose();else{const t=this.exploreMenu;this.selectedSubmenu="Explore",t.forEach((t=>{t.isVisible=!0,t.isItemsActive=!1}))}},onClose(){this.submenu.isActive=!1,this.submenu.data=[],this.submenu.ref=""},manageSubmenu(t,e,n){t.preventDefault(),this.submenu.isActive=!this.submenu.isActive,this.submenu.data=e,this.submenu.ref=n,this.selectedSubmenu=e.section},manageSubmenuLevel3(t){this.level3Menus.length||this.cacheLevel3Menus();const e=this.level3Menus.find((e=>e.classList.contains(t)));e&&(this.submenuClasses(),e.classList.toggle("show"))},isPageHasCategory(){"undefined"!=typeof assignedCategory&&localStorage.setItem("userSignUpPage",assignedCategory)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},getState(){if(null!==localStorage.getItem("isChooseAccountState"))return["userState","isChooseAccountState"].forEach((t=>localStorage.removeItem(t))),!1}}})}}}(jQuery),YUNOHeaderRevamp=(jQuery,{headerRevamp:function(){let t;Vue.directive("closable",{bind(e,n,o){t=t=>{t.stopPropagation();const{handler:a,exclude:r}=n.value;let i=!1;r.forEach((e=>{if(!i){const n=o.context.$refs[e];i=n[0].contains(t.target)}})),e.contains(t.target)||i||o.context[a]()},document.addEventListener("click",t),document.addEventListener("touchstart",t)},unbind(){document.removeEventListener("click",t),document.removeEventListener("touchstart",t)}}),Vue.component("yuno-header-revamp",{props:["hasnav","hasscrollenabled","haslogoAlignment","postsignup","hassearchbar","fetchAPI","options"],template:'\n            <div class="isSticky">\n                <div>\n                    <section class="appPrompt" v-if="appInfo.platform === \'android\'">\n                        <div class="container">\n                            <ul class="colGrid">\n                                <li class="appIcon">\n                                    <div class="closePrompt">\n                                        <i @click="onPromptClose()" class="fa fa-times" aria-hidden="true"></i>\n                                    </div>\n                                    <figure class="appMedia">\n                                        <div class="iconWrap">\n                                            <img :src="appInfo.icon" :alt="appInfo.shortName">\n                                        </div>\n                                        <figcaption class="appInfo">\n                                            <p class="infoTitle">{{ appInfo.shortName }}</p>\n                                            <p class="infoCaption" v-if="appInfo.hasOS === \'android\'">{{ appInfo.androidCaption }}</p>\n                                        </figcaption>\n                                    </figure>\n                                </li>\n                                <li>\n                                    <b-button tag="a"\n                                        :href="appInfo.hasOS === \'android\' ? appInfo.androidURL : \'\'"\n                                        target="_blank"\n                                        rel="nofollow"\n                                        @click="onPromptView($event)"\n                                        class="yunoPrimaryCTA small">\n                                        Open\n                                    </b-button>\n                                </li>\n                            </ul>\n                        </div>\n                    </section>\n                    <template v-if="userInfo.error">\n                        <header id="yunoHeader" class="yunoHeader">\n                            <div class="container noOverflow">\n                                <nav class="navbar navbar-expand-lg">\n                                    <figure class="logo navbar-brand">\n                                        <a :href="getHomeURL"><img :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                    </figure>\n                                    <button class="navbar-toggler" data-toggle="collapse" data-target="#yunoMainNav" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">\n                                        <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                    </button>\n                                    <div class="collapse navbar-collapse yunoMainNav" id="yunoMainNav">\n                                        <ul class="navbar-nav justify-content-end align-items-center w-100">\n                                            <li class="nav-item">\n                                                <a \n                                                    href="/logout">\n                                                    Logout\n                                                </a>\n                                            </li>\n                                        </ul>\n                                    </div>\n                                </nav>\n                            </div>\n                        </header>\n                        <main id="yunoMain">\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </main>\n                    </template>\n                    <template v-else>\n                        <div class="notificationBar" :class="{ notificationHide: notification.isHidden, notificationShow: !notification.isHidden, notVisibleInDOM: !notification.isVisible }">\n                            <a :href="notification.url" target="_blank">\n                                {{ notification.label }}\n                            </a>\n                            <span class="material-icons-outlined" @click="hideNotificationBar">close</span>\n                        </div>\n                        <yuno-header \n                            :isnav="hasnav !== undefined ? hasnav : undefined" \n                            :logoAlignment="haslogoAlignment !== undefined ? haslogoAlignment : undefined" \n                            :scrollenabled="hasscrollenabled !== undefined ? hasscrollenabled : undefined"\n                            :postsignup="postsignup"\n                            :hassearchbar="hassearchbar"\n                            :options="{zohoMeta: options !== undefined ? options.zohoMeta : \'\'}">\n                        </yuno-header>\n                    </template>\n                    <b-modal \n                        :active.sync="config.unauthorizedModal" \n                        :width="450" \n                        :can-cancel="[\'escape\', \'x\']" \n                        :on-cancel="unauthorizedModalClose"\n                        class="yunoModal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">Session Expired</h2>\n                            </div>\n                            <div class="modalBody">\n                                <div class="wrapper">\n                                    <p>{{sessionExpired}}</p>\n                                </div>\n                            </div>\n                            <div class="modalFooter">\n                                <div class="unauthorizedLogin">\n                                    <a \n                                        @click.prevent="setState()"\n                                        href="#">\n                                        <span class="g_icon"></span>\n                                        <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                    </a>\n                                </div>\n                            </div>\n                    </b-modal>\n                </div>\n                <b-modal \n                    :active.sync="loginModal.modal" \n                    :width="360" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="loginModalClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="loginModal.modal">\n                            <div class="modalHeader">\n                                <h3 class="modalTitle">\n                                    <template v-if="loginModal.active === \'login\'">\n                                        Log in\n                                    </template>\n                                    <template v-else>\n                                        Sign up\n                                    </template>\n                                </h3>\n                            </div>\n                            <div class="modalBody">\n                                <template v-if="loginModal.active === \'login\'">\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <div class="ctaWrapper">\n                                        <button class="googleLogin width70" @click="setState">\n                                            <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                        </button>\n                                    </div>\n                                    <p class="footerCaption">\n                                        {{loginModal[loginModal.active].footer.label}} <a @click="updateLoginState($event, \'signup\')" :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                    </p>\n                                </template>\n                                <template v-else>\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observer"\n                                        ref="commonSignupObserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                            <b-field label="Phone number">\n                                                <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                    <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                    <p class="error">{{errors[0]}}</p>\n                                                </validation-provider>    \n                                            </b-field>\n                                            <div class="ctaWrapper">\n                                                <button class="googleLogin" type="submit">\n                                                    <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                                </button>\n                                            </div>\n                                            <p class="helperCaption">\n                                                {{loginModal[loginModal.active].helper.label}} <a @click="updateLoginState($event, \'login\')" :href="loginModal[loginModal.active].helper.actionURL">{{loginModal[loginModal.active].helper.actionLabel}}</a>\n                                            </p>\n                                            <p class="footerCaption" v-if="loginModal[loginModal.active].footer.isActive">\n                                                {{loginModal[loginModal.active].footer.label}} <a :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                            </p>\n                                        </form>\n                                    </validation-observer>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </div>\n            ',data(){return{tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,appInfo:{shortName:"Yuno Learning",name:"Yuno Learning",icon:this.$store.state.themeURL+"/assets/images/yunoLogo.svg",androidURL:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",hasOS:"android",androidCaption:"FREE - In the Google Play Store",platform:"",daysHidden:1,daysReminder:90},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}},loginModal:{modal:!1,active:"",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",login:{title:"Log into your Yuno Learning account",hasMobile:!1,cta:"Login with Google",footer:{label:"Don't have an account yet?",actionLabel:"Sign Up",actionURL:"#"}},signup:{title:"Sign up to create a free account on Yuno Learning",hasMobile:!0,cta:"Sign up with Google",helper:{label:"Already a user?",actionLabel:"Login",actionURL:"#"},footer:{label:"Are you an instructor?",isActive:!0,actionLabel:"Sign up here",actionURL:"/ielts/become-an-instructor"}}},notification:{isHidden:!1,isVisible:!1,label:"Free summer online workshop for kids and teenagers",url:"/free-summer-workshops-for-kids",hideNotificationDate:""}}},computed:{...Vuex.mapState(["user","userRole","userInfo","userProfile","header","footer","config","apiTokenExpiryTime","apiTokenRefresh"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},emptyStates:()=>({state:"dataNotFound"}),getSignInURL(){return this.$store.getters.getSignInURL}},async created(){this.emitEvents()},mounted(){this.loginStatus()},methods:{hideNotificationBar(){this.notification.isVisible=!1,this.notification.isHidden=!0},setSigninProps(){localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const{zohoMeta:t={}}=this.$props.options||{},e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general";this.signIn={...this.signIn,categoryURL:`/${e}`,landing_page:{url:window.location.origin+window.location.pathname,title:document.title},productCode:t.productcode||"",leadStatus:t.leadstatus||"",utmSource:YUNOCommon.getQueryParameter("utm_source"),utmCampaign:YUNOCommon.getQueryParameter("utm_campaign"),utmMedium:YUNOCommon.getQueryParameter("utm_medium"),adGroupID:YUNOCommon.getQueryParameter("adgroupid"),adContent:YUNOCommon.getQueryParameter("ad_content"),utmTerm:YUNOCommon.getQueryParameter("utm_term"),gclid:YUNOCommon.getQueryParameter("gclid"),content:{type:t.content_type||"",id:t.content_id||""}}},isFirefoxPrivate(t){null!==t&&t?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},emitEvents(){void 0!==Event.$emit&&(Event.$on("manageLogin",((t,e)=>{this.manageLogin(t,e)})),Event.$on("initLoginModal",((t,e,n)=>{this.manageLogin(t,"signup"),this.loginModal[this.loginModal.active].footer.isActive=!1})),Event.$on("initSignupModal",(t=>{this.manageLogin(t,"signup")})))},updateLoginState(t,e){t.preventDefault(),this.loginModal.active=e},initCommonSignup(){this.setState()},loginModalClose(){setTimeout((()=>{this.loginModal.active=""}),300),localStorage.removeItem("paymentState"),localStorage.removeItem("isQuiz")},manageLogin(t,e){this.loginModal.modal=!0,this.loginModal.active=e},onPromptClose(){const t=this.appInfo.daysHidden;YUNOCommon.setCookie("yunoAppPrompt2",!0,t),this.detectPlatform()},onPromptView(t){this.detectPlatform()},detectPlatform(){if(/iPhone|iPad|iPod/i.test(window.navigator.userAgent))this.appInfo.platform="ios";else if(/Android/i.test(window.navigator.userAgent)){const t=YUNOCommon.getCookie("yunoAppPrompt2");this.appInfo.platform=void 0===t&&"android"}else this.appInfo.platform=!1},unauthorizedModalClose(){window.location.href="/logout"},roleSpecificAPI(t,e){"Learner"===t&&this.detectPlatform(),void 0!==Event.$emit&&Event.$emit("gotUserRole",t,e)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):(this.user.isLoggedin=!1,this.detectPlatform(),void 0===this.$props.hasnav||this.$props.hasnav?(this.fetchPreLoginMenu(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin))},initTokenTime(t){let e=parseInt(t-10),n=parseInt(6e4*e);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data;this.config.yunoAPIToken="Bearer "+e.token,this.tokenExpiry.payload.token="Bearer "+e.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.doneRefreshAPIToken(t)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data,n=10;if(e.minutes<=n){let t={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(t)}else this.initTokenTime(e.minutes)}},fetchAPITokenExpiryTime(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.gotAPITokenExpiryTime(t)}};this.$store.dispatch("postData",n)},gotUserInfo(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){const e=t.response.data.data;this.userRole.data=e.role,this.userRole.success=!0,this.userProfile.data=e,this.userProfile.success=!0,(void 0===this.$props.hasnav||this.$props.hasnav)&&0!==this.header.data.length&&this.fetchPostLoginMenu(e.role),this.$props.fetchAPI&&this.fetchPostLoginMenu(e.role);const n=localStorage.getItem("skipSignUp"),o=localStorage.getItem("paymentState");if("Instructor"===e.role&&"de-active"===e.account_status&&"/account-disabled/"!==window.location.pathname)return window.location.href=YUNOCommon.config.host()+"/account-disabled",!1;"pending"===e.is_signup_completed&&"/sign-up/"!==window.location.pathname&&"/instructor-sign-up/"!==window.location.pathname&&null===n&&null===o&&setTimeout((()=>{"Instructor"===e.role?window.location.href=YUNOCommon.config.host()+"/instructor-sign-up":window.location.href=YUNOCommon.config.host()+"/sign-up"}),50),this.roleSpecificAPI(e.role,e)}else this.userInfo.error=!0,this.header.success=!0,this.footer.success=!0},fetchUserInfo(){const t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",t)},highlightCurrentPage(t){const e=t.response.data.data.map((t=>({...t,isActive:t.url===window.location.href})));this.header.data=e},gotPostLoginMenu(t){200===t?.response?.data?.code&&(this.highlightCurrentPage(t),Event.$emit("gotUserMenu"))},fetchPostLoginMenu(t){const e={apiURL:YUNOCommon.config.headerMenuAPIV2(isLoggedIn,t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:this.gotPostLoginMenu};this.$store.dispatch("fetchData",e)},gotPreLoginMenu(t){200===t?.response?.data?.code&&(this.highlightCurrentPage(t),Event.$emit("gotPreLoginMenu"))},fetchPreLoginMenu(){const t={userID:isLoggedIn,orgID:0},e={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",callback:!0,callbackFunc:this.gotPreLoginMenu};this.$store.dispatch("fetchData",e)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(primary, i) in footerData.primary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="primary.type === \'withIcon\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :class="item.icon"\n                                                    :key="j">\n                                                    <a rel="nofollow" :href="item.url">\n                                                        <span>{{item.label}}</span>\n                                                    </a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'basicNoLinks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined && item.helper !== \'\' ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <span>{{item.label}}</span>\n                                                    <small class="ml-2 helper" v-if="item.helper !== undefined && item.helper !== \'\'">{{item.helper}}</small>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'blocks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <h4>{{item.label}}</h4>\n                                                    <p>{{item.description}}</p>\n                                                    <a :href="item.cta.url">{{item.cta.label}}</a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'withOrderList\'">\n                                        <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j">\n                                                    {{item.label}}\n                                                </li>\n                                            </template>\n                                        </ul>\n                                        <b-button tag="a"\n                                            :href="primary.cta.url"\n                                            target="_blank"\n                                            rel="nofollow noopener"\n                                            class="yunoSecondaryCTA">\n                                            {{ primary.cta.label }}\n                                        </b-button>\n                                    </template>\n                                    <template v-if="primary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <div class="col-12">\n                                <div class="spacer"></div>\n                            </div>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(secondary, i) in footerData.secondary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="secondary.type === \'basic\'">\n                                        <ul class="linkList" :class="secondary.type">\n                                            <li v-if="secondary.title !== undefined" class="listTitle">\n                                                <h3>{{ secondary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in secondary.items">\n                                                <li \n                                                    :key="j"\n                                                >\n                                                    <template v-if="isContainsObject(item.url)">\n                                                        <a :href="getEnvironmentUrl(item.url)">{{item.label}}</a>\n                                                    </template>\n                                                    <template v-else>\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </template>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="secondary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <img class="appQR" alt="Yuno Android APP QR Code" width="96" height="96" :src="wpThemeURL + \'/assets/images/appQR.webp\'">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                        <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="!isnav">\n                            <div class="col-12">\n                                <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                    <li class="copy">© Yunolearnung. {{currentYear}}</li>\n                                    <li class="copy">\n                                        <a href="/privacy-policy/" target="_blank">Privacy Policy</a>\n                                    </li>\n                                    <li class="copy">\n                                        <a href="/terms-of-use/" target="_blank">Terms of use</a>\n                                    </li>\n                                    <li v-if="false">\n                                        <figure class="logo">\n                                            <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                        </figure>\n                                    </li>\n                                </ul>\n                            </div>\n                        </div>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"************",footerData:{primary:[{title:!1,type:"withIcon",items:[{label:"Check internet speed",url:"http://yunolearning.speedtestcustom.com",icon:"wifiSpeed"},{label:"Zoom test",url:"https://zoom.us/test",icon:"zoomTest"}]},{title:"Teach Online",type:"blocks",items:[{label:"Instructor",description:"Create an instructor account and start teaching",cta:{label:"Sign up for instructor",url:"/become-an-instructor/"}},{label:"Academy",description:"Create your academy, publish courses and get new learners",cta:{label:"Create academy",url:"/create-academy/"}}]},{title:"Coming Soon",type:"basicNoLinks",items:[{label:"Developer Guide",helper:"",url:"#"},{label:"API Documentation",url:"#"},{label:"Postman Collection",url:"#"}]},{title:"Yuno Business",type:"basicNoLinks",items:[{label:"Yuno for Study Abroad Consultants",helper:"",url:"/for-study-abroad-and-immigration-companies/"},{label:"Yuno for Career Conselors",helper:"",url:"#"},{label:"Yuno for Skilling Companies",helper:"",url:"#"},{label:"Yuno for Learning & Development",helper:"",url:"#"},{label:"Yuno for IT Service Companies",helper:"",url:"#"},{label:"Yuno for Airlines & Hospitality",helper:"",url:"#"}]}],secondary:[{title:"IELTS",type:"basic",items:[{label:"IELTS Online Classes",url:"/ielts"},{label:"IELTS Free Study Material",url:"/ielts/collection/ielts-study-material-and-practice-tests/"},{label:"IELTS Reading",url:"/blog/how-to-prepare-for-ielts-reading-test"},{label:"IELTS Writing Task 1",url:"/blog/how-to-prepare-for-ielts-writing-task-1"},{label:"IELTS Writing Task 2",url:"/blog/how-to-prepare-for-ielts-writing-task-2"},{label:"IELTS Listening",url:"/blog/how-to-prepare-for-ielts-listening-test"},{label:"IELTS Speaking",url:"/blog/how-to-prepare-for-ielts-speaking-test"},{label:"IELTS Practice Tests",url:"/ielts/practice-tests"},{label:"Free IELTS Resources",url:"/ielts"}]},{title:"English Speaking",type:"basic",items:[{label:"Spoken English Classes",url:{dev:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',stage:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',prod:'/search/?state=%7B"category":%5B2811%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"limit":20,"offset":0,"personalization":"all","price_per_hour":10000,"total_duration":24%7D'}},{label:"English Speaking for Working Professionals",url:"/english-speaking/professionals/"},{label:"English Speaking for Students",url:"/english-speaking/students/"},{label:"Spoken English for Kids",url:"/english-speaking/kids/"},{label:"English Speaking for Teenagers",url:"#"},{label:"English Speaking for Home makers",url:"/english-speaking/homemakers/"},{label:"English Speaking for Interview",url:"/english-speaking/interview/"}]},{title:"More Resources",type:"basic",items:[{label:"Microsoft Excel Video Tutorials",url:"/microsoft-excel/resources/"},{label:"Python Programming Videos",url:"/python-programming/resources/"},{label:"Vedic Maths Tricks",url:"/vedic-maths/resources/"},{label:"Learn Vedic Maths",url:"/vedic-maths/"},{label:"Coding Classes for kids",url:"/coding-for-kids/"}]},{type:"stackBlock",items:[{title:"Our Mobile App",type:"appCTA",items:[{label:"Get the Android App",url:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",icon:"android"}]},{title:"Social Media Channels",type:"iconOnly",items:[{label:"Facebook",url:"https://www.facebook.com/yunolearning",icon:"facebook"},{label:"Twitter",url:"https://twitter.com/YunoLearning",icon:"twitter"},{label:"Linkedin",url:"https://www.linkedin.com/company/yunolearning",icon:"linkedin"},{label:"Instagram",url:"https://www.instagram.com/yuno.learning/",icon:"instagram"},{label:"Youtube",url:"https://www.youtube.com/channel/UCTVIUtnoO5c103cSWXIBelQ",icon:"youtube"}]}],cta:{label:"Check out our blog",url:"/blog/"}}]}}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL},host:()=>YUNOCommon.config.pickHost(),getEnvironmentUrl(){return t=>{if(!this.isContainsObject(t))return t;const e={"https://dev.yunolearning.com":"dev","https://www.yunolearning.com":"prod",default:"stage"},n=e[this.host]||e.default;return this.host+t[n]}}},async created(){},methods:{isContainsObject:t=>"object"==typeof t&&null!==t}})}});Vue.component("yuno-tabs-v2",{props:{animated:{type:Boolean,default:!1},destroyOnHide:{type:Boolean,default:!1}},template:'\n        <section class="yunoTabsWrapper" :class=[filterResult.tabs.wrapperClass]>\n            <b-tabs \n                class="yunoTabsV5" \n                v-model="filterResult.tabs.activeTab" \n                @input="tabChange" \n                :animated="animated"\n                :destroy-on-hide="destroyOnHide"\n            >\n                <b-tab-item \n                    v-for="(tab, i) in filterResult.tabs.items"\n                    :visible="tab.isVisible"\n                    :key="i"\n                    :label="tab.label">\n                    <slot :name="tab.slug"></slot>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user","filterResult"])},async created(){},destroyed(){},mounted(){},methods:{tabChange(t){this.$emit("tabChange",t)}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){t.response.data.data}},fetchReferralCode(){const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const t=sessionStorage.getItem("isLHSMenu");null===t||this.isMobile?this.reduce=!1:(this.reduce="true"===t,this.sidebarToggle(!0))},sidebarToggle(t){t||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(t){let e=parseInt(t-10),n=parseInt(6e4*e);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data;this.config.yunoAPIToken="Bearer "+e.token,this.tokenExpiry.payload.token="Bearer "+e.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.doneRefreshAPIToken(t)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(t){if(void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){const e=t.response.data.data,n=10;if(e.minutes<=n){let t={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(t)}else this.initTokenTime(e.minutes)}},fetchAPITokenExpiryTime(t){const e=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(t),callback:!0,callbackFunc:function(t){return e.gotAPITokenExpiryTime(t)}};this.$store.dispatch("postData",n)},extractSlugFromURL(t){const e=t.replace(/\/$/,"").split("/");""===e[e.length-1]&&e.pop();return e[e.length-1]},manageCurrentPage(t){const e=t=>t.replace(/\/$/,""),n=e(window.location.origin+window.location.pathname);t.forEach((t=>{t.items.forEach((t=>{t.is_active=n===e(t.url);let o=!1;t.sub_items.forEach((a=>{a.is_active=n===e(a.url),a.is_active&&a.parent_id===t.id&&(o=!0)})),t.is_expended=!!o}))}))},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},gotPostLoginMenu(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";t?e=t.response.data.data:(e=this.header.data,this.header.success=!0),this.manageCurrentPage(e),this.header.data=e,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(t){const e={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,o={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(t){return n.gotPostLoginMenu(t)}};this.$store.dispatch("fetchData",o)},manageOrgAdmin(t){const{host:e}=YUNOCommon.config,{has_org:n,org_id:o}=t;null===sessionStorage.getItem("activeOrg")&&(n?o.length>1?(window.location.href=`${e()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(o[0].id)):window.location.href=`${e()}/create-organization-account`)},gotUserInfo(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code||!t){let e="";if(t?e=t.response.data.data:(e=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(e.role),this.userRole.data=e.role,this.userProfile.data=e,this.userProfile.success=!0,e.role,"Learner"===e.role&&this.fetchReferralCode(),"Learner"===e.role&&"pending"===e.is_signup_completed){const t=localStorage.getItem("userState");window.location.pathname+window.location.search!==t&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}e.role,this.$emit("userInfo",e)}},fetchUserInfo(){const t=this,e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(e){return t.gotUserInfo(e)}};this.$store.dispatch("fetchData",e)},getStorage(){const t=this.storage;let e=Number(JSON.parse(JSON.stringify(t.version)));lastStorage=t.name+"V"+--e,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(t.name+"V"+t.version);if(null!==n){const t=JSON.parse(n);this.header.data=t.menu}this.loginStatus()},setStorage(){const t=this.storage,e={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(e))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const t=this.storage;sessionStorage.removeItem(t.name+"V"+t.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(t){if(this.generateCode.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;Event.$emit("fetchReferralCode")}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("postData",e)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(t,e){0!==e.sub_items.length&&t.preventDefault(),"generate-code"===e.slug&&t.preventDefault(),"Switch Account"===e.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Learn":"Insights",generateClass:t=>t.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.data.code){const e=t.response.data.data;this.referralCode.data=e}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const t=this,e={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(e){return t.gotReferralCode(e)}};this.$store.dispatch("fetchData",e)},gotCode(t){if(this.moduleWithoutTab.loading=!1,void 0!==t.response&&void 0!==t.response.data&&201===t.response.data.code){t.response.data;this.fetchReferralCode()}else{const e=t.response.data;this.$buefy.toast.open({duration:5e3,message:`${e.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const t=this,e={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(e){return t.gotCode(e)}};this.$store.dispatch("postData",e)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:t=>"Learner"===t?"Referral":"Referral Earnings",isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)},copyToClipboard(t){let e=document.getElementById(t);e.select(),e.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(t){return!!YUNOCommon.findInArray(t,this.userRole.data)}}}),Vue.component("yuno-availability-v2",{props:{data:{type:Array,required:!1},options:{type:Object,required:!1}},template:'\n        <div>\n          \t<ul class="hoursWrapper">\n\t\t\t\t<li class="item" v-for="(item, index) in data" :key="index">\n\t\t\t\t\t<div class="itemWrapper">\n\t\t\t\t\t\t<div class="day">{{ item.day }}</div>\n\t\t\t\t\t\t<div class="slots">\n\t\t\t\t\t\t\t<b-field>\n\t\t\t\t\t\t\t\t<b-switch v-model="item.isDayOff"\n  \t\t\t\t\t\t\t\t\t@input="onOpen($event ? false : true, index)"\n\t\t\t\t\t\t\t\t\t:true-value="false"\n\t\t\t\t\t\t\t\t\t:false-value="true">\n\t\t\t\t\t\t\t\t\t<template v-if="item.isDayOff">\n\t\t\t\t\t\t\t\t\t\tUnavailable\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\tAvailable\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t</b-switch>\n\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="hours" v-if="!item.isDayOff"> \n\t\t\t\t\t\t<template v-for="(slot, slotIndex) in item.availability">\n\t\t\t\t\t\t\t<div class="hourWrapper">\n\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\ttag="div"\n\t\t\t\t\t\t\t\t\tclass="chooseHour" \n\t\t\t\t\t\t\t\t\t:rules="{required:true, isOverlapping: slot.isOverlapping}" \n\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t\t:customMessages="{ required: message.required}"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<div :class="{ hasError: errors && errors.length > 0 }">\n\t\t\t\t\t\t\t\t\t\t<b-dropdown\n\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\tv-model="slot.startTime"\n\t\t\t\t\t\t\t\t\t\t\taria-role="list"\n\t\t\t\t\t\t\t\t\t\t\tclass="filterMenu"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n\t\t\t\t\t\t\t\t\t\t\t\t<span><template>Start</template> {{ slot.startTime  }}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t<template v-for="time in timesList">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown-item\n  \t\t\t\t\t\t\t\t\t\t\t\t\t@click="onFilterItemSelect(slot, time, \'start\', item)"\n  \t\t\t\t\t\t\t\t\t\t\t\t\t:key="time"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value="slot.startTime"\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="listitem">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span>{{time}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown-item>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</b-dropdown>\n\t\t\t\t\t\t\t\t\t\t<p class="error" v-if="false">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t:customMessages="{ required: message.required, is_not: message.isNot, greaterThen: message.greaterThen }"\n\t\t\t\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\t\t\t\tclass="chooseHour"\n\t\t\t\t\t\t\t\t\t:rules="{required:true, is_not: slot.startTime, isOverlapping: slot.isOverlapping, isEndTime: slot.isEndTime}" \n\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<div :class="{ hasError: errors && errors.length > 0 }">\n\t\t\t\t\t\t\t\t\t\t<b-dropdown\n\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\tv-model="slot.endTime"\n\t\t\t\t\t\t\t\t\t\t\taria-role="list"\n\t\t\t\t\t\t\t\t\t\t\tclass="filterMenu"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n\t\t\t\t\t\t\t\t\t\t\t\t<span>End {{ slot.endTime }}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n\t\t\t\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t\t\t\t<template v-for="time in timesList">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-dropdown-item\n  \t\t\t\t\t\t\t\t\t\t\t\t\t@click="onFilterItemSelect(slot, time, \'end\', item)"\n  \t\t\t\t\t\t\t\t\t\t\t\t\t:key="time"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:value="slot.endTime"\n\t\t\t\t\t\t\t\t\t\t\t\t\<EMAIL>="slot.endTime = time"\n\t\t\t\t\t\t\t\t\t\t\t\t\taria-role="listitem">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span>{{time}}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-dropdown-item>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</b-dropdown>\n\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t<b-button \n  \t\t\t\t\t\t\t\t\t@click="removeSlot(index, slotIndex)" \n\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA iconOnly removeSlot noBG">\n\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined">close</span>\n\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t<b-tooltip label="Copy time to all"\n\t\t\t\t\t\t\t\t\ttype="is-dark"\n\t\t\t\t\t\t\t\t\tposition="is-top">\n\t\t\t\t\t\t\t\t\t<b-button\n\t\t\t\t\t\t\t\t\t\tv-if="slotIndex === 0"\n\t\t\t\t\t\t\t\t\t\t@click="copySlot(item, slotIndex)" \n\t\t\t\t\t\t\t\t\t\tclass=" iconOnly copySlot noBG">\n\t\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined">content_copy</span>\n\t\t\t\t\t\t\t\t\t</b-button>    \n\t\t\t\t\t\t\t\t</b-tooltip>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t<template>\n\t\t\t\t\t\t\t<div class="addSlotWrapper">\n\t\t\t\t\t\t\t\t<b-button \n\t\t\t\t\t\t\t\t\t@click="addSlot(index)" \n\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA addSlot noBG">\n\t\t\t\t\t\t\t\t\tAdd Hours\n\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</div>\n\t\t\t\t</li>\n          \t</ul>\n        </div>\n  ',data:()=>({message:{required:"Required",isNot:"Value should not be same as start time",error:""},timesList:["12:00 AM","12:30 AM","1:00 AM","1:30 AM","2:00 AM","2:30 AM","3:00 AM","3:30 AM","4:00 AM","4:30 AM","5:00 AM","5:30 AM","6:00 AM","6:30 AM","7:00 AM","7:30 AM","8:00 AM","8:30 AM","9:00 AM","9:30 AM","10:00 AM","10:30 AM","11:00 AM","11:30 AM","12:00 PM","12:30 PM","1:00 PM","1:30 PM","2:00 PM","2:30 PM","3:00 PM","3:30 PM","4:00 PM","4:30 PM","5:00 PM","5:30 PM","6:00 PM","6:30 PM","7:00 PM","7:30 PM","8:00 PM","8:30 PM","9:00 PM","9:30 PM","10:00 PM","10:30 PM","11:00 PM","11:30 PM"]}),methods:{onOpen(t,e){t&&0===this.data[e].availability.length&&this.data[e].availability.push({startTime:"",endTime:""})},timeToMinutes(t){if(!t)return NaN;const[e,n]=t.trim().split(/\s+/);if(!e||!n)return NaN;let[o,a]=e.split(":").map(Number);return"PM"===n&&12!==o?o+=12:"AM"===n&&12===o&&(o=0),60*o+a},onFilterItemSelect(t,e,n,o){console.log(t,e,n,o),"start"===n?t.startTime=e:"end"===n&&(t.endTime=e),this.manageOverlappingSlot(t,o),this.manageEndTime(t)},manageEndTime(t){const e=this.timeToMinutes(t.startTime),n=this.timeToMinutes(t.endTime);""!==t.startTime&&""!==t.endTime?t.isEndTime=n<=e:t.isEndTime=!1},manageOverlappingSlot(t,e){if(""===t.startTime||""===t.endTime)return void(t.isOverlapping=!1);const n=this.timeToMinutes(t.startTime),o=this.timeToMinutes(t.endTime);t.isOverlapping=!1,e.availability.forEach((e=>{if(e===t)return;const a=this.timeToMinutes(e.startTime),r=this.timeToMinutes(e.endTime);n<r&&a<o&&(t.isOverlapping=!0)}))},addSlot(t){this.data[t].availability.push({startTime:"",endTime:"",isOverlapping:!1,isEndTime:!1})},removeSlot(t,e){this.data[t].availability.splice(e,1),0===this.data[t].availability.length&&(this.data[t].isDayOff=!0)},copySlot(t,e){const n=JSON.parse(JSON.stringify(t.availability));this.data.forEach((t=>{t.isDayOff||(t.availability=JSON.parse(JSON.stringify(n)))}))}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-add-classroom",{template:'\n        <yuno-page-grid\n            :authorizedRoles="authorizedRoles"\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="false"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <template v-if="moduleWithoutTab.loading">\n                        <figure class="infiniteSpinner">\n                            <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                        </figure>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <div class="add-classroom">\n                            <div class="container">\n\t\t\t\t\t\t\t\t<div class="mainHeader">\n\t\t\t\t\t\t\t\t\t<h1>Add Classroom</h1>\n\t\t\t\t\t\t\t\t</div>\n                                <div class="classroomContainer" :class="{\'details\': hideSearchField}">\n\t\t\t\t\t\t\t\t\t<template v-if="!hideSearchField">\n\t\t\t\t\t\t\t\t\t\t<yuno-search-location \n\t\t\t\t\t\t\t\t\t\t\t:map="map"\n\t\t\t\t\t\t\t\t\t\t\t@confirmLocation="showClassroomDetail"\n\t\t\t\t\t\t\t\t\t\t\t@updateSelectedLocation="updateSelectedLocation"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</yuno-search-location>\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t<yuno-classroom-detail\n\t\t\t\t\t\t\t\t\t\t\tref="classroomDetail"\n\t\t\t\t\t\t\t\t\t\t\t@hideClassroomDetail="hideClassroomDetail"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t</yuno-classroom-detail>\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t<div class="mapWrapper" ref="googleMap"></div>\n                                </div>\n                            </div>\n                        </div>\n                    </template>\n                </div>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({apiURL:null,isMiniSidebar:!1,pageHeader:{title:"Org Settings"},authorizedRoles:["org-admin"],storage:{name:"orgSettings",version:1},hideSearchField:!1,map:null,marker:null,geocoder:null}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","filters","filterResult","moduleWithoutTab","form","timeSlots"]),wpThemeURL(){return this.$store.state.themeURL},isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data)}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading:{get(){return this.userInfo.loading}},isPageReady:{get(){let t="";return t=!this.user.isLoggedin||this.userInfo.success,t}}},methods:{initGoogleMap(){if(!this.$refs.googleMap)return void console.error("Map container not found");const t={zoom:12,center:{lat:28.6139,lng:77.209},mapTypeControl:!1,streetViewControl:!1,fullscreenControl:!0,draggable:!0,zoomControl:!0};try{this.map=new google.maps.Map(this.$refs.googleMap,t),this.geocoder=new google.maps.Geocoder}catch(t){console.error("Error initializing Google Maps:",t)}},updateSelectedLocation(t){this.map.setCenter(t.geometry.location),this.map.setZoom(15),this.marker&&this.marker.setMap(null)},showClassroomDetail(){this.hideSearchField=!0},hideClassroomDetail(){this.hideSearchField=!1},onUserInfo(t){YUNOCommon.findInArray(this.authorizedRoles,t.role)&&this.fetchOrgInfo()},setupForm(){const t={isRequired:!1,isDisabled:!1,isLoading:!1};this.form.fields=[{label:"School / College / Commercial Building ",placeholder:"Select",type:"dropdown",name:"type",options:[{label:"School",value:"SCHOOL"},{label:"College",value:"COLLEGE"},{label:"Commercial Building ",value:"COMMERCIAL_BUILDING"}],required:!0},{label:"Name of Place",placeholder:"Enter the name of place",type:"text",name:"name",required:!0},{label:"Short Description",placeholder:"Enter short description here",type:"textarea",name:"short_description",required:!0},{label:"Long Description",placeholder:"Enter long description here",type:"textarea",name:"long_description",required:!0},{label:"Complete Address",placeholder:"Enter address",type:"text",name:"formatted_address",readonly:!0,...t},{label:"Address",placeholder:"Enter address",type:"text",name:"address_1",required:!0},{label:"Floor (Optional)",placeholder:"Enter floor",type:"text",name:"floor",...t},{label:"Landmark (Optional)",placeholder:"Enter landmark",type:"text",name:"landmark",...t},{label:"Area / Sector / Locality (Optional)",placeholder:"Enter area / sector / locality",type:"text",name:"address_2",...t},{title:"Facilities",class:"faciltiesCheckbox",isGrouped:!0,fields:[{label:"Car Parking",placeholder:"",type:"checkbox",name:"car_parking",hasChildren:!0,...t},{label:"Self Parking",placeholder:"",type:"checkbox",name:"self_parking",class:"addMargin",...t},{label:"Valet Parking",placeholder:"",type:"checkbox",name:"valet_parking",class:"addMargin",...t},{label:"Bike Parking",placeholder:"",type:"checkbox",name:"bike_parking",...t}]},{title:"Open Hours (Only of places)",type:"timeSlots"},{title:"Classroom Detail",type:"classDetailForm",class:"classDetailForm",classrooms:[{fields:[{label:"Class Title",placeholder:"Enter class title",type:"text",name:"class_title",required:!0},{type:"groupedTextDropdown",class:"classroomFloor",groups:[{label:"Floor",placeholder:"GroundPlus/OnePlus",type:"dropdown",name:"classroom_floor_type",options:[{label:"GroundPlus",value:"GROUND_PLUS"},{label:"OnePlus",value:"ONE_PLUS"}],required:!0},{label:"Floor Number",placeholder:"Enter floor number",type:"text",name:"classroom_floor_number",required:!0}]},{label:"Classroom area (Sqft)",placeholder:"Select",type:"dropdown",name:"classroom_area",options:Array.from({length:50},((t,e)=>({label:`${e+1}`,value:`${e+1}`}))),required:!0},{label:"Seating Capacity",placeholder:"Enter seating capacity",type:"text",name:"seating_capacity",required:!0},{title:"Facilities",class:"groupCheckbox",isGrid:"true",type:"gridCheckbox",fields:[{label:"Wifi",type:"checkbox",name:"wifi"},{label:"Whiteboard",type:"checkbox",name:"whiteboard"},{label:"Blackboard",type:"checkbox",name:"blackboard"},{label:"Projector",type:"checkbox",name:"projector"},{label:"LCD Monitor",type:"checkbox",name:"lcd_monitor"},{label:"Air Conditioning",type:"checkbox",name:"air_conditioning"},{label:"Power Backup",type:"checkbox",name:"power_backup"}]},{label:"Computer Terminal",placeholder:"Computer Terminal",type:"text",name:"computer_terminals",required:!0}]}]}];const e={org_id:this.activeOrg(),type:null,name:"",short_description:"",long_description:"",formatted_address:this.searchQuery,address_1:"",address_2:"",floor:"",floor_type:"",landmark:"",city:"",state_name:"",state_code:"",country_name:"",country_code:"",postal_code:"",car_parking:!1,self_parking:!1,valet_parking:!1,bike_parking:!1,class_title:"",classroom_floor_type:"",classroom_floor_number:"",seating_capacity:"",classroom_area:"",wifi:!1,whiteboard:!1,blackboard:!1,projector:!1,lcd_monitor:!1,air_conditioning:!1,power_backup:!1,computer_terminals:0,google_map:null};this.form.payload=e,this.timeSlots.data=[{day:"Monday",availability:[],isDayOff:!0},{day:"Tuesday",availability:[],isDayOff:!0},{day:"Wednesday",availability:[],isDayOff:!0},{day:"Thursday",availability:[],isDayOff:!0},{day:"Friday",availability:[],isDayOff:!0},{day:"Saturday",availability:[],isDayOff:!0},{day:"Sunday",availability:[],isDayOff:!0}]},activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},gotOrgInfo(t){const{response:{data:{code:e,data:n}={}}={}}=t;200===e&&(setTimeout((()=>{this.initGoogleMap()}),100),this.setupForm())},fetchOrgInfo(){const t={apiURL:YUNOCommon.config.generic("org",!1,!1,this.activeOrg()),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:this.gotOrgInfo};this.$store.dispatch("fetchData",t)}}}),Vue.component("yuno-classroom-detail",{template:'\n        <div class="classroomDetailForm">\n            <div class="goBack mb-3" @click="hideClassroomDetail">\n                <span class="material-icons headline6 noBold onSurfaceVariant">arrow_back</span>\n                <span class="onSurfaceVariant subtitle2">Classroom and Place Details</span>\n            </div>\n            <yuno-org-form\n                :fields="form.fields"\n                :payload="form.payload"\n                :timeSlots="timeSlots.data"\n                @submitForm="submitForm"\n            >\n            </yuno-org-form>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["form","timeSlots","userInfo"])},methods:{activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},hideClassroomDetail(){this.$emit("hideClassroomDetail")},createClassroomPayload(t,e){this.form.payload.classrooms||(this.form.payload.classrooms=[]),this.form.payload.classrooms=[];const n=[];for(const e in t)if(e.startsWith("class_title_")){const t=e.split("_")[2];n.push(t)}n.forEach((n=>{const o={place_id:e,title:t[`class_title_${n}`],floor:{type:t[`classroom_floor_type_${n}`],number:t[`classroom_floor_number_${n}`]},area:t[`classroom_area_${n}`],seating_capacity:t[`seating_capacity_${n}`],facilities:{wifi:t[`wifi_${n}`]??!1,whiteboard:t[`whiteboard_${n}`]??!1,blackboard:t[`blackboard_${n}`]??!1,projector:t[`projector_${n}`]??!1,lcd_monitor:t[`lcd_monitor_${n}`]??!1,air_conditioning:t[`air_conditioning_${n}`]??!1,power_backup:t[`power_backup_${n}`]??!1,computer_terminals:t[`computer_terminals_${n}`]}};this.form.payload.classrooms.push(o)}))},createPlacePayload(t){this.form.payload.place||(this.form.payload.place={});const e={org_id:this.activeOrg(),type:t.type,name:t.name,short_description:t.short_description,long_description:t.long_description,address:{type:"OTHER",title:t.type?t.type.toLowerCase():"",formatted_address:t.formatted_address,address_1:t.address_1,address_2:t.address_2,floor:{type:"IN"===this.form.payload.country_code?"GROUNDPLUS":"GROUND",number:t.floor},landmark:t.landmark,city:t.city,state:{name:t.state_name,code:t.state_code},country:{id:0,name:t.country_name,code:t.country_code},pin_zip:t.postal_code,coordinates:{latitude:t.google_map.coordinates.latitude,longitude:t.google_map.coordinates.longitude},google_maps:{place_id:t.google_map.place_id,type:t.google_map.types,colloquial_area:t.google_map.colloquial_area,locality:t.google_map.locality,sublocality:t.google_map.sublocality,neighborhood:t.google_map.neighborhood,postal_code:t.google_map.postal_code,floor:"",landmark:"",administrative_area:t.google_map.administrative_area}},facilities:{car_parking:{self_parking:t.self_parking,valet_service:t.valet_parking},bike_parking:t.bike_parking},open_hours:this.timeSlots.data.map((t=>({day:t.day.substring(0,3).toUpperCase(),is_available:!t.isDayOff,time_slot:t.availability.length>0?t.availability.map((t=>({start:this.convertTo24Hour(t.startTime),end:this.convertTo24Hour(t.endTime)}))):[{start:"00:00",end:"00:00"}]})))};this.form.payload.place=e,console.log(this.form.payload.place,"placePayload")},convertTo24Hour(t){if(!t)return"00:00";if((t=t.trim()).includes(":")&&!t.match(/\s*(AM|PM)\s*/i)){const[e,n]=t.split(":"),o=parseInt(e);if(o>=0&&o<=23)return`${e.padStart(2,"0")}:${n.padStart(2,"0")}`}const e=t.match(/(\d+):(\d+)\s*(AM|PM)/i);if(!e){const e=t.match(/(\d+)(\d{2})\s*(AM|PM)/i);if(e){const[t,n,o,a]=e;return this.convertTimeTo24Hour(parseInt(n),parseInt(o),a)}return"00:00"}const[n,o,a,r]=e;return this.convertTimeTo24Hour(parseInt(o),parseInt(a),r)},convertTimeTo24Hour:(t,e,n)=>("PM"===n.toUpperCase()&&t<12?t+=12:"AM"===n.toUpperCase()&&12===t&&(t=0),`${t.toString().padStart(2,"0")}:${e.toString().padStart(2,"0")}`),classroomPosted(t){if(console.log(t.response),200===t.response.status){console.log(t.response,"submittedclassroomform");const e=t.index||0;this.form.payload.classrooms&&e+1<this.form.payload.classrooms.length?this.submitClassroomPayload(e+1):(this.form.payload.classrooms=[],this.form.payload.place={},this.form.isLoading=!1,this.showToastMessage(t.response.data.message),this.hideClassroomDetail())}},submitClassroomPayload(t=0){let e=this.form.payload.classrooms[t];const n={apiURL:YUNOCommon.config.classroom("createClassroom",!1),module:"gotData",store:"form",payload:e,callback:!0,callbackFunc:e=>{e.index=t,this.classroomPosted(e)}};this.$store.dispatch("postData",n)},formPosted(t){if(200===t.response.status){console.log(t.response);let e=t.response.data.data.placeId;this.showToastMessage(t.response.data.message),this.createClassroomPayload(this.form.payload,e),this.form.payload.classrooms&&this.form.payload.classrooms.length>0&&this.submitClassroomPayload(0)}},submitForm(){this.form.isLoading=!0,this.createPlacePayload(this.form.payload);let t=this.form.payload.place;const e={apiURL:YUNOCommon.config.Places("create",!1),module:"gotData",store:"form",payload:t,callback:!0,callbackFunc:t=>this.formPosted(t)};this.$store.dispatch("postData",e)},showToastMessage(t){this.$buefy.toast.open({duration:5e3,message:`${t}`,position:"is-bottom"})}},watch:{"form.payload.car_parking"(t){!0===t?(this.form.payload.self_parking=!0,this.form.payload.valet_parking=!0):(this.form.payload.self_parking=!1,this.form.payload.valet_parking=!1)}}}),Vue.component("yuno-org-form",{props:{fields:{type:Array,required:!0},payload:{type:Object,required:!0},timeSlots:{type:Array,required:!1}},template:'\n        <div class="yunoFormWrapper">\n            <validation-observer \n                tag="div" \n                class="observer"\n                ref="orgSettingsFormObserver" \n                v-slot="{ handleSubmit, invalid }"\n            >\n                <form @submit.prevent="handleSubmit(initForm)">\n                    <template v-for="(field, i) in fields">\n                        <template v-if="field.type === \'text\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-input\n                                        :class="classes"\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        :readonly="field?.readonly"\n                                    >\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'textarea\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-input\n                                    type="textarea"\n                                    v-model="payload[field.name]"\n                                    :placeholder="field.placeholder"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-input>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'checkbox\'">\n                            <b-field :key="i">\n                                <b-checkbox \n                                    :value="payload[field.name]" \n                                    v-model="payload[field.name]"\n                                >\n                                    {{ field.label}}\n                                </b-checkbox>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'dropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="dropdownChange($event)"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.value"\n                                            :disabled="option.isDisabled"\n                                        >\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'googleFontFamilydropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.family"\n                                        >\n                                            {{ option.family }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'multiSelectDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <validation-provider \n                                        tag="div" \n                                        class="fieldWrapper" \n                                        :rules="{\n                                            required: field.isRequired\n                                        }" \n                                        v-slot="{ errors, classes }"\n                                    >\n                                        <b-dropdown\n                                            v-model="field.selected"\n                                            :class="classes"\n                                            multiple\n                                            :disabled="field.disabled"\n                                            aria-role="list"\n                                            @change="multiSelectDropdownChange($event, field)"\n                                        >\n                                            <template #trigger>\n                                                <b-button>\n                                                    <template v-if="field.selected.length">\n                                                        <div class="selected">\n                                                            <template v-for="(subOption, k) in field.selected">\n                                                                {{ subOption.name }}{{ k < field.selected.length - 1 ? \', \' : \'\' }}\n                                                            </template>    \n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="placeholder">{{ field.placeholder }}</div>\n                                                    </template>\n                                                    <div class="material-icons">expand_more</div>    \n                                                </b-button>\n                                            </template>\n                                            <b-dropdown-item \n                                                :value="subOption"\n                                                aria-role="listitem"\n                                                v-for="(subOption, k) in field.options"\n                                                :key="k"\n                                            >\n                                                <span>{{ subOption.name }}</span>\n                                            </b-dropdown-item>\n                                        </b-dropdown>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'groupDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="industry"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="groupDropdownChange($event, field)"\n                                    >\n                                        <optgroup \n                                            :label="option.label"\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                        >\n                                            <option\n                                                v-for="(subOption, k) in option.sub_industry"\n                                                :key="k"\n                                                :value="subOption"\n                                            >\n                                                {{ subOption.label }}\n                                            </option>\n                                        </optgroup>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'upload\'">\n                            <b-field :label="field.label" :key="i" class="uploadField">\n                                <p class="helper">{{ field.placeholder }}</p>\n                                <b-upload\n                                    v-model="payload[field.name]"\n                                    accept="image/*"\n                                    class="file-label"\n                                >\n                                    <span class="file-cta">\n                                        <span class="material-icons-outlined">file_upload</span>\n                                        <span class="file-label">{{ field.cta }}</span>\n                                    </span>\n                                    <span class="file-name" v-if="payload[field.name]">\n                                        {{ payload[field.name].name }}\n                                    </span>\n                                </b-upload>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'colorpicker\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-colorpicker\n                                    :color-formatter="(color) => color.toString(\'hex\')"\n                                    representation="square"\n                                    @input="updateColor($event, field.name)"\n                                    :value="payload[field.name]"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-colorpicker>\n                            </b-field>\n                        </template>\n                        <template v-if="field?.isGrouped">\n  \t\t\t\t\t\t\t<h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>\n\t\t\t\t\t\t\t<div :class="field?.class" :key="i">\n\t\t\t\t\t\t\t\t<template v-for="(input, j) in field.fields">\n\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'text\'">\n\t\t\t\t\t\t\t\t\t\t<div :class="input?.class" :key="j">\n\t\t\t\t\t\t\t\t\t\t\t<b-field :label="input.label">\n\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\t\t\t\t\t\t\t\tclass="fieldWrapper" \n\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired: input.isRequired\n\t\t\t\t\t\t\t\t\t\t\t\t\t}" \n\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="checkbox11"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[input.name]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="input.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="input.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="input.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'checkbox\'">\n  \t\t\t\t\t\t\t\t\t\t<div :class="input?.class" :key="j">\n\t\t\t\t\t\t\t\t\t\t\t<b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-checkbox \n\t\t\t\t\t\t\t\t\t\t\t\t\t:value="payload[input.name]" \n\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[input.name]"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t{{ input.label}}\n\t\t\t\t\t\t\t\t\t\t\t\t</b-checkbox>\n\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'dropdown\'">\n\t\t\t\t\t\t\t\t\t\t<b-field :label="input.label" :key="i">\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="input.loading">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-skeleton height="40px"></b-skeleton>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t<b-select\n\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[input.name]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="input.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="field.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="input.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t@input="dropdownChange($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(option, j) in input.options"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="j"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.value"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="option.isDisabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</b-field>\n                        \t\t\t</template>\n\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t</div>\n                        </template>\n                        <template v-if="field.type === \'timeSlots\'">\n                            <h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>\n                            <yuno-availability-v2\n                                :data="timeSlots"\n                            >\n                            </yuno-availability-v2>\n                        </template>\n\t\t\t\t\t\t<template v-if="field.type ===\'classDetailForm\'">\n\t\t\t\t\t\t\t<h2 class="subtitle1 onSurface" >{{ field?.title}}</h2>\n\t\t\t\t\t\t\t<template v-for="(classroom, classroomIndex) in field.classrooms">\n\t\t\t\t\t\t\t\t<div :class="field?.class" :key="i + \'-\' + classroomIndex" >\n\t\t\t\t\t\t\t\t\t<div class="deleteClass" v-if="classroomIndex > 0" @click="removeClassroom(field,classroomIndex)">\n\t\t\t\t\t\t\t\t\t\t<span class="material-icons">close</span>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div :class="classroom?.isGrouped ? \'grouped\' : \'\'">\n  \t\t\t\t\t\t\t\t\t\t<template v-for="(input, j) in classroom.fields">\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'text\'">\n\t\t\t\t\t\t\t\t\t\t\t\t<div class="mb-2" :key="input.name + j">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field :label="input.label">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="fieldWrapper" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired: input.isRequired\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[input.name + \'_\' + classroomIndex]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="input.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="input.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="input.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'dropdown\'">\n\t\t\t\t\t\t\t\t\t\t\t\t<b-field :label="input.label" >\n\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="input.loading">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-skeleton height="40px"></b-skeleton>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="i"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[input.name + \'_\' + classroomIndex]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="input.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="field.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="input.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@input="dropdownChange($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(option, j) in input.options"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="option.label + \'_\' + j"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.value"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="option.isDisabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n                                            <template v-if="input.type === \'groupedTextDropdown\'">\n\t\t\t\t\t\t\t\t\t\t\t\t<div :class="input?.class" :key="j">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-for="(group, k) in input.groups">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="group.type === \'text\'">\n  \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field :label="group.label" :key="k">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<validation-provider \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttag="div" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclass="fieldWrapper" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:rules="{\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trequired: group.isRequired\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}" \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-slot="{ errors, classes }"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-input\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:class="classes"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[group.name + \'_\' + classroomIndex]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="group.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="group.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="group.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-input>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p class="error">{{errors[0]}}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</validation-provider>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-if="group.type === \'dropdown\'">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field :label="group.label" :key="k">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-select\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[group.name + \'_\' + classroomIndex]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:placeholder="group.placeholder"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="group.disabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:loading="group.isLoading"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t@input="dropdownChange($event)"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<option\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-for="(option, j) in group.options"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:key="option.label + \'_\' + j"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:value="option.value"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t:disabled="option.isDisabled"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</option>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-select>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if="input.type === \'gridCheckbox\'">\n\t\t\t\t\t\t\t\t\t\t\t\t<h2 class="subtitle1 onSurface mt-3" >{{ input?.title}}</h2>\n\t\t\t\t\t\t\t\t\t\t\t\t<div :class="input.class" :key="j">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<template v-for="(checkbox,k) in input.fields">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div :key="checkbox.name + \'_\'  + k">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<b-checkbox \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tv-model="payload[checkbox.name + \'_\' + classroomIndex]"\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{{ checkbox.label}}\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-checkbox>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</b-field>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<div class="primaryColor subtitle2" @click="addNewClassroom(field)">\n\t\t\t\t\t\t\t\t<span>+</span>\n\t\t\t\t\t\t\t\t<span class="addClass">Add more classroom</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</template>\n                    </template>\n                    <div class="ctaWrapper">\n                        <b-button\n                            native-type="reset"\n                            @click="clearForm"\n                            class="yunoPrimaryCTA wired fat">\n                            Cancel\n                        </b-button>    \n                        <b-button\n                            native-type="submit"\n                            :loading="form.isLoading"\n                            :disabled="form.isLoading"\n                            class="yunoPrimaryCTA fat">\n                            Submit\n                        </b-button>    \n                    </div>\n                </form>\n            </validation-observer>\n        </div>\n    ',data:()=>({industry:null,defaultPayload:[],classrooms:[]}),computed:{...Vuex.mapState(["user","categoryTaxonomy","form"])},watch:{industryData:{handler(t,e){t!==e&&(this.industry=t)},deep:!0}},async created(){},destroyed(){},mounted(){this.init()},methods:{updateColor(t,e){this.form.payload[e]=t.toString("hex")},init(){this.defaultPayload=JSON.parse(JSON.stringify(this.form.payload))},clearForm(){this.$refs.orgSettingsFormObserver.reset(),this.form.payload=JSON.parse(JSON.stringify(this.defaultPayload))},jsonToFormData(t){const e=new FormData;for(let n in t)Array.isArray(t[n])&&t[n].every((t=>"object"==typeof t))?e.append(n,JSON.stringify(t[n])):e.append(n,t[n]);return e},initForm(){this.$emit("submitForm",this.jsonToFormData(this.form.payload))},dropdownChange(t){},groupDropdownChange({parent:t,slug:e},n){"sub_industry"===n.name&&(this.form.payload.industry=t,this.form.payload.sub_industry=e)},multiSelectDropdownChange(t,e){this.form.payload[e.name]=t},addNewClassroom(t){const e=t.classrooms[0];t.classrooms.push(e)},removeClassroom(t,e){t.classrooms.splice(e,1)}}}),Vue.component("yuno-search-location",{props:{map:{type:Object,required:!1,default:null}},template:'\n        <div class="searchLocation">\n            <h2 class="subtitle1 pb-2">Search place on Google map</h2>\n            <div class="searchContainer">\n                <b-field>\n                    <b-autocomplete\n                        ref="searchInput"\n                        v-model="searchQuery"\n                        :data="filteredAddress"\n                        @input="searchLocation"\n                        @select="handleSelectedAddress"\n                        placeholder="Search place on Google to add in classroom"\n                        custom-class="yunoSearchAddress"\n                        :clearable="true">\n                            <template slot-scope="props">\n                                <div style="display:flex;flex-direction:column;gap:5px">\n                                    <span class="main_text">{{ props.option.structured_formatting.main_text }}</span>\n                                    <span class="secondary_text">{{ props.option.structured_formatting.secondary_text }}</span>\n                                </div>\n                            </template>\n                            <template #empty>\n                                <div class="no-results">No results</div>\n                            </template>\n                    </b-autocomplete>\n                </b-field>\n                <div class="cta">\n                    <b-button\n                        class="yunoPrimaryCTA"\n                        :disabled="selectedLocation === null"\n                        @click="confirmLocation"\n                    >\n                        Continue to Add\n                    </b-button>\n                </div>\n            </div>\n        </div>\n    ',data:()=>({searchQuery:"",selectedLocation:null,searchResults:[],placesService:null}),computed:{...Vuex.mapState(["form","filters","timeSlots"]),wpThemeURL(){return this.$store.state.themeURL},filteredAddress(){return this.searchResults}},methods:{activeOrg(){const t=this.userInfo.data.current_state.org_id;if(t)return t},confirmLocation(){this.$emit("confirmLocation")},searchLocation(t){if(this.searchQuery=t,t){if(window.google&&window.google.maps&&google.maps.places){(new google.maps.places.AutocompleteService).getPlacePredictions({input:t},((t,e)=>{e===google.maps.places.PlacesServiceStatus.OK?this.searchResults=t:this.searchResults=[]}))}}else this.searchResults=[]},handleSelectedAddress(t){const e=window.google?.maps?.places;if(t&&e){const e=t.place_id;new google.maps.places.PlacesService(this.$props.map).getDetails({placeId:e},((t,n)=>{n===google.maps.places.PlacesServiceStatus.OK&&t.geometry&&(this.$emit("updateSelectedLocation",t),this.selectedLocation={lat:t.geometry.location.lat(),lng:t.geometry.location.lng(),address:t.formatted_address,placeId:t.place_id},this.searchQuery=t.formatted_address,this.fetchLocationDetail(e))}))}else this.selectedLocation=null,this.filters.data=[]},extractCoordinates:t=>({latitude:t.geometry?.location?.lat||0,longitude:t.geometry?.location?.lng||0}),extractAddressInfo(t){const e=e=>t.find((t=>t.types.includes(e))),n=e("postal_code"),o=e("administrative_area_level_1"),a=e("country"),r=e("locality"),i=e("neighborhood"),s=e("colloquial_area");var l;const u=[...(l="sublocality",t.filter((t=>t.types.includes(l))))].sort(((t,e)=>{const n=t.types.find((t=>t.startsWith("sublocality_level_"))),o=e.types.find((t=>t.startsWith("sublocality_level_")));return n||o?n?o?parseInt(n.split("_").pop())-parseInt(o.split("_").pop()):-1:1:0})),c={};for(let t=1;t<=7;t++){const n=e(`administrative_area_level_${t}`);c[`administrative_area_level_${t}`]=n?.long_name||null}return{postal_code:n?.long_name||"",state:o,country:a,locality:r?.long_name||"",neighborhood:i?.long_name||"",colloquial_area:s?.long_name||"",sublocality:{sublocality_level_1:u[0]?.long_name||"",sublocality_level_2:u[1]?.long_name||"",sublocality_level_3:u[2]?.long_name||"",sublocality_level_4:u[3]?.long_name||null,sublocality_level_5:u[4]?.long_name||null},administrative_area:c,country:a?.long_name||"",country_code:a?.short_name||"",state_name:o?.long_name||"",state_code:o?.short_name||""}},updateFormPayload(t,e,n){const o=this.form.payload;o.postal_code=e.postal_code,o.country=e.country,o.country_name=e.country,o.country_code=e.country_code,o.city=e.locality,o.state_name=e.state_name,o.state_code=e.state_code,o.formatted_address=t?.formatted_address,o.name=t?.name,o.latitude=n.latitude,o.longitude=n.longitude},updateTimeSlots(t){t&&t.length?(this.timeSlots.data.forEach((t=>{t.availability=[],t.isDayOff=!1})),t.forEach((t=>{const e=t.indexOf(":");if(-1===e)return;const n=t.substring(0,e).trim(),o=t.substring(e+1).trim(),[a,r]=o.split("–").map((t=>t.trim())),i=this.timeSlots.data.find((t=>t.day===n));i&&i.availability.push({startTime:a,endTime:r,isOverlapping:!1,isEndTime:!1})}))):this.timeSlots.data.forEach((t=>{t.availability=[{isOverlapping:!1,isEndTime:!1}],t.isDayOff=!0}))},updatePayload(t){const e=t.address_components||[],n=t,o=(this.form.payload,t.opening_hours&&t.opening_hours.weekday_text),a=this.extractAddressInfo(e),r=this.extractCoordinates(n);this.form.payload.google_map={place_id:t.place_id||"",types:t.types||[],colloquial_area:a.colloquial_area,locality:a.locality,sublocality:a.sublocality,neighborhood:a.neighborhood,postal_code:a.postal_code,administrative_area:a.administrative_area,country:a.country,coordinates:{latitude:t.geometry?.location?.lat||0,longitude:t.geometry?.location?.lng||0},opening_hours:t.opening_hours?.weekday_text||[]},this.updateFormPayload(n,a,r),this.updateTimeSlots(o)},gotfetchedLocationDetail(t){if(void 0!==t.response&&void 0!==t.response.data&&200===t.response.status){this.filters.data=t.response.data.data;let e=this.filters.data;this.updatePayload(e)}},fetchLocationDetail(t){const e={placeID:t},n={apiURL:YUNOCommon.config.Places("fetchMapPlaceDetails",e),module:"gotData",store:"filters",callback:!0,callbackFunc:t=>this.gotfetchedLocationDetail(t)};this.$store.dispatch("fetchData",n)}},mounted(){window.google&&window.google.maps&&(this.placesService=new google.maps.places.PlacesService(document.createElement("div")))}});