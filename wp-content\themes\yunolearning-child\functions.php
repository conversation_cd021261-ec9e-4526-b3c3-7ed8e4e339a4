<?php
//add file with global scope :: start 
require_once ABSPATH.'wp-content/themes/yunolearning-child/inc/global/logger/logger.php';
// Obtain an instance of the logger
$logger = WP_Structured_Logger::get_instance();
require_once('inc/global/error-codes/error_codes.php');
//i18n
require_once('inc/global/i18n/Locale.php');
$ynLocale = new YonoLocale();
$ynTimeZone = new YunoTimeZone();
require_once('inc/global/Utility.php');
$Utility = new Utility;
require_once('inc/razorpay/razorpay-php/Razorpay.php');
require_once('inc/vendor/autoload.php');
//add file with global scope :: end 

// Near the top of the file, after other includes
// Include OAuth related files
require_once('inc/mvc/controllers/OauthController.php');
require_once('inc/mvc/models/OauthModel.php');

use Razorpay\Api\Api;
use inc\Aws\Pinpoint\PinpointClient;
//use inc\aws\Aws\S3\S3;
use inc\Aws\PinpointEmail\PinpointEmailClient;
use inc\Aws\AwsClient;
use inc\Aws\Kinesis\KinesisClient;
use inc\Aws\Exception\AwsException;
use League\OAuth2\Client\Provider\GenericProvider;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;

require_once('inc/awsConnect.php');
require_once('inc/test-attribute.php');
require_once('inc/general-functions.php');
require_once('inc/events/user-activity-feed.php');
require_once('inc/segment/vendor/autoload.php');
require_once('inc/async/vendor/autoload.php');
require_once('inc/pool/vendor/autoload.php');
require_once('inc/load-more.php');
require_once('inc/writing-tests-rating.php');
require_once('inc/reading-listening-tests-rating.php');
require_once('inc/user-delete-account.php');
require_once('inc/user-information.php');
require_once('inc/email-template.php');
require_once('inc/class-embed-code.php');
require_once('inc/class-rating.php');
require_once('inc/google-calendar-api.php');
require_once('inc/google-calendar-api-v2.php');
require_once('inc/google-calendar-events.php');
require_once('inc/enroll-class.php');
 require_once('inc/user-cookies.php');
require_once('inc/zoom-cron.php');
require_once('inc/zoom/ssoCreateUser.php');
require_once('inc/zoho-crm.php');
require_once('inc/my-classes.php');
require_once('inc/test/test-functions.php');
require_once('inc/classes/class-functions.php');
require_once('inc/products/product-functions.php');
require_once('inc/razorpay/razorpay-functions.php');
require_once('inc/Google/google-contact-functions.php');
require_once('inc/instructor-functions.php');
require_once('inc/instructor/schedule-class.php');
require_once('config/yuno-config.php');
require_once('yuno-hooks/yuno-hooks.php');
require_once('inc/csvimport/csvimport.php');
require_once('inc/admin-functions/manually-enroll-user-to-class.php');
require_once('inc/batches/create-batch-class.php');
require_once('inc/classes/elastic_search/UserElasticSearch.php');
require_once('inc/yuno-seo/SeoClass.php');
add_filter( 'wpseo_robots', 'yoast_robots_page_index_update' );
//use Spatie\Async\Pool;

//This is where we decide where to route the request
if (strpos($_SERVER["REQUEST_URI"], '/v3/')) {
  //This is api v3 so use api v3
  require_once('api_v3.php');
} else if (strpos($_SERVER["REQUEST_URI"], '/v2/')) {
  //This is api v2 so use api v2
  require_once('api_v2.php');
} else {
  //This is api v2 (or the default since most recent)
  require_once('api_v1.php');
}
require_once('inc/libraries/Google/autoload.php');
// cron started
require_once('inc/cron/batch_event_per_class.php');
require_once('inc/cron/class_pending_task_process.php');
require_once('inc/cron/recording_event_per_class.php');
require_once('inc/cron/zoom_event_per_class.php');



/// 28 sep-2023 -- start adding functions file parts

require_once('functions-part/Zoho-Crm.php');
$ZohoCrm = new Zoho_Crm();
require_once('functions-part/Google-Auth.php');
$GoogleAuth = new Google_Auth();
require_once('functions-part/Enqueue-Scripts.php');
$EnqueueScripts = new Enqueue_Scripts();
require_once('functions-part/Admin-Menu.php');
$AdminMenu = new Admin_Menu();
require_once('functions-part/WpHead.php');
$WpHead = new WpHead();
require_once('functions-part/Quiz.php');
$Quiz = new Quiz();
require_once('functions-part/Notification.php');
$Notifications = new Notification;
require_once('functions-part/Elastic-Search.php');
$ElasticSearch = new Elastic_Search;
require_once('functions-part/Init-Post-Type.php');
$InitPostType = new Init_Post_Type;
require_once('functions-part/ZoomActivities.php');
$ZoomActivities = new ZoomActivities;
require_once('functions-part/JwtAuthenticationActivities.php');
$JwtAuthActivities = new JwtAuthenticationActivities;
require_once('functions-part/TribeEventsActivities.php');
$TribeEventsActivities = new TribeEventsActivities;
require_once('functions-part/ClassActivities.php');
$ClassRelatedActivities = new ClassActivities;
require_once('functions-part/InstructorsActivities.php');
$InstructorsActivities = new InstructorsActivities;
require_once('functions-part/CourseActivities.php');
$CourseActivities = new CourseActivities;
require_once('functions-part/CacheActivities.php');
$CacheActivities = new CacheActivities;
require_once('functions-part/CronActivities.php');
$CronActivities = new CronActivities;
require_once('functions-part/SegmentPostsActivities.php');
$SegmentPostsActivities = new SegmentPostsActivities;
require_once('functions-part/BatchActivities.php');
$BatchActivities = new BatchActivities;
require_once('functions-part/ReferralCodeActivities.php');
$ReferralCodeActivities = new ReferralCodeActivities;
require_once('functions-part/OrgActivities.php');
$OrgActivities = new OrgActivities;
require_once('functions-part/LogsActivities.php');
$LogsActivities = new LogsActivities;
require_once('functions-part/EnrollmentActivities.php');
$EnrollmentActivities = new EnrollmentActivities;
require_once('functions-part/CsvActivities.php');
$CsvActivities = new CsvActivities;
require_once('functions-part/LearnersActivities.php');
$LearnersActivities = new LearnersActivities;
require_once('functions-part/UsersActivities.php');
$UsersActivities = new UsersActivities;
require_once('functions-part/TokenActivities.php');
$TokenActivities = new TokenActivities;
require_once('functions-part/TestActivities.php');
$TestActivities = new TestActivities;
require_once('functions-part/UrlActivities.php');
$UrlActivities = new UrlActivities;
require_once('functions-part/CookiesActivities.php');
$CookiesActivities = new CookiesActivities;
require_once('functions-part/MetaActivities.php');
$MetaActivities = new MetaActivities;
require_once('functions-part/PaymentsActivities.php');
$PaymentsActivities = new PaymentsActivities;
require_once('functions-part/TaxonomyActivities.php');
$TaxonomyActivities = new TaxonomyActivities;
require_once('functions-part/DatabaseQueryActivities.php');
$DatabaseQueryActivities = new DatabaseQueryActivities;
require_once('functions-part/DeviceActivities.php');
$DeviceActivities = new DeviceActivities;
require_once('functions-part/OwaspscanActivities.php');
$OwaspscanActivities = new OwaspscanActivities;
// require_once('inc/classes/background_process/WP_Background_Zoho_Lead_Process.php');
// global $wpb_zoho_lead_process;
// $wpb_zoho_lead_process = new WP_Background_Zoho_Lead_Process();
/// 28 sep-2023 -- end adding functions file parts

/**
 * Global wrapper for OauthModel->createYunoAuthDataArray
 */
function create_yuno_auth_data_array($user_id, $response, $user_details, $email, $sub_id, $org_details = null, $decodedPayload = []) {
    $oauthModel = new V4\OauthModel();
    return $oauthModel->createYunoAuthDataArray($user_id, $response, $user_details, $email, $sub_id, $org_details, $decodedPayload);
}

/******************************************************************
 ***************** Start: Custom global variables ******************
 *******************************************************************/
global $Demo_Class_count_Limit;
global $LimitCount;
global $Demo_Class_Category_ID;
global $Demo_Class_Category;
$LimitCount             = "10000";
$Demo_Class_count_Limit = (int) $LimitCount;
$Demo_Class_Category_ID = "1906";


add_action('after_setup_theme', [$WpHead, 'avada_lang_setup']);

/*
Get Script and Style IDs
Adds inline comment to your frontend pages
View source code near the <head> section
Lists only properly registered scripts
@ https://digwp.com/2018/08/disable-script-style-added-plugins/
*/
function shapeSpace_inspect_script_style()
{
  global $WpHead;
  return $WpHead->shapeSpace_inspect_script_style();
}

// add_action('wp_print_scripts', 'shapeSpace_inspect_script_style');

/**
 * deregister function will unlink files (script and styles) from the page or post,
 * where no need.
 */

add_action('wp_enqueue_scripts', [$EnqueueScripts, 'deregisterResources'], 999);


add_filter('clean_url', [$WpHead,  'addAsyncScript'], 11, 1);


add_filter('clean_url', [$WpHead, 'addDeferScript'], 11, 1);





add_action('wp_enqueue_scripts', [$EnqueueScripts, 'loadCommonStyles']);

add_action('wp_enqueue_scripts', [$EnqueueScripts, 'loadCommonJS'], 8);

add_action('wp_enqueue_scripts', [$EnqueueScripts, 'pageSpecificResource'], 10);



add_action('wp_footer', [$WpHead, 'hook_js']);

function add_rel_preload($html, $handle, $href, $media)
{
  global $WpHead;
  return $WpHead->add_rel_preload($html, $handle, $href, $media);
}

// add_filter( 'style_loader_tag', 'add_rel_preload', 10, 4 );


add_action('wp_head', [$WpHead, 'hook_snippet']);



add_action('after_setup_theme', [$WpHead, 'yunoThemeSetup']);

/*************************************************
 ******** Start: Change Admin Menu Text ***********
 **************************************************/


add_filter('gettext', [$WpHead, 'yuno_change_admin_text_strings'], 20, 3);

/******************************************************************
 ********************  Start: Get user lead id *********************
 *******************************************************************/

function get_lead_id($userLeadId)
{
global $UsersActivities;
return $UsersActivities->get_lead_id($userLeadId);
}

function enrol_from_dashboard($classIDs)
{
  global $EnrollmentActivities;
  return $EnrollmentActivities->enrol_from_dashboard($classIDs);
}

function yuno_get_user_role()
{
  global $UsersActivities;
  return $UsersActivities->yuno_get_user_role();
}


function addTestActivitytoZoho($AllPostedData, $userId)
{
  global $ZohoCrm;
  return $ZohoCrm->addTestActivitytoZoho($AllPostedData, $userId);
}

add_action('wp_ajax_yuno-tast-activity', [$TestActivities, 'yuno_tast_activity_callback']);
add_action('wp_ajax_nopriv_yuno-tast-activity', [$TestActivities, 'yuno_tast_activity_callback']);


add_action('wp_ajax_yuno-test-start-activity', [$TestActivities, 'yuno_test_start_activity_callback']);
add_action('wp_ajax_nopriv_yuno-test-start-activity', [$TestActivities, 'yuno_test_start_activity_callback']);




//add_action('wp_ajax_yuno-require-login-to-finsh-test', 'yuno_require_login_to_finsh_test');
//add_action('wp_ajax_nopriv_yuno-require-login-to-finsh-test', 'yuno_require_login_to_finsh_test');

function yuno_require_login_to_finsh_test()
{
  global $TestActivities;
  return $TestActivities->yuno_require_login_to_finsh_test();
}

function attemptedQuizDetail($yquizTotalQuestions, $yquizGlobalPoints, $yquizCatName, $yuserID, $yquizID, $relativeScore)
{
  global $Quiz;
  return $Quiz->attemptedQuizDetail($yquizTotalQuestions, $yquizGlobalPoints, $yquizCatName, $yuserID, $yquizID, $relativeScore);
}

//add_action('wp_ajax_yuno-attempted-quiz', 'yuno_attempted_quiz');
//add_action('wp_ajax_nopriv_yuno-attempted-quiz', 'yuno_attempted_quiz');

function yuno_attempted_quiz()
{
  global $Quiz;
  return $Quiz->yuno_attempted_quiz();
}

add_action('widgets_init', [$WpHead, 'theme_slug_widgets_init']);


add_action('wp_ajax_yuno-filter-practice-test', [$Quiz, 'yuno_filter_practice_test']);
add_action('wp_ajax_nopriv_yuno-filter-practice-test', [$Quiz, 'yuno_filter_practice_test']);

add_action('wp_ajax_yuno-filter-practice-test-listening',  [$Quiz, 'yuno_filter_practice_test_listening']);
add_action('wp_ajax_nopriv_yuno-filter-practice-test-listening',  [$Quiz, 'yuno_filter_practice_test_listening']);

function get_squence_max_value()
{
  global $Quiz;
  return $Quiz->get_squence_max_value();
}

function test()
{
  global $TestActivities;
  return $TestActivities->test();
}


// REMOVE WP EMOJI

remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');
remove_action('admin_print_scripts', 'print_emoji_detection_script');
remove_action('admin_print_styles', 'print_emoji_styles');

// $attachments = array( WP_CONTENT_DIR . '/uploads/file_to_attach.zip' );
// $headers = 'From: My Name <<EMAIL>>' . "\r\n";
// wp_mail( '<EMAIL>', 'subject', 'message', $headers);

function yuno_require_login_to_finsh_test_new()
{
 global $TestActivities;
 return $TestActivities->yuno_require_login_to_finsh_test_new();
}


// add_action( 'init','yuno_user_set_action');

add_action( 'init', [$UsersActivities, 'yuno_user_visit_init']);

// add_action('wp_head', [$UsersActivities, 'yuno_user_set_action']);

function testing()
{
  global $TestActivities;
  return $TestActivities->testing();
}
/************************************************
 ****************** Start: check url  ************
 *************************************************/

function check_url()
{
  global $UrlActivities;
  return $UrlActivities->check_url();
}

/************************************************
 ********* Start: Execute Cron Daily  ************
 *************************************************/

/************************************************
 *********** Start: Send Zoho Activity ************
 *************************************************/

function cron_test()
{
  global $CronActivities;
  return $CronActivities->cron_test();
}

/************************************************
 ******************* Start: Unset cron to 0 ******
 *************************************************/

function cron_values()
{
  global $CronActivities;
  return $CronActivities->cron_values();
}

/**********************************************************
 ************ Start: Yuno Review Practice Test  *************
 ***********************************************************/
add_action('wp_ajax_yuno-review-practice-test', [$TestActivities, 'yuno_review_practice_test']);
add_action('wp_ajax_nopriv_yuno-review-practice-test', [$TestActivities, 'yuno_review_practice_test']);



add_filter('wpseo_json_ld_output', [$WpHead, 'bybe_remove_yoast_json'], 10, 1);

add_filter('disable_wpseo_json_ld_search', '__return_true');





/********************************************************************************
 ********* Start: Hooking up our functions to WordPress filters ******************
 *********************************************************************************/

add_filter('wp_mail_from', [$WpHead, 'wpb_sender_email']);
add_filter('wp_mail_from_name', [$WpHead, 'wpb_sender_name']);


/************************************************************************
 ********************* Start: User Login Before Redirect *****************
 *************************************************************************/
function yuno_get_loggedin_user_enrolled_classes($user_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->yuno_get_loggedin_user_enrolled_classes($user_id);
}


// Css For secondary logo 
add_action('wp_head', [$WpHead, 'add_css_head']);



add_filter('optin_monster_api_final_output', [$UsersActivities, 'hide_optimonster_popup_if_user_on_trial_account']);


remove_filter('the_content', 'wpautop');

function version_id()
{
  global $WpHead;
  return $WpHead->version_id();
}

/****************************************************************************
 ********************* Start: Adding Groups to classes  **********************
 *****************************************************************************/

function add_yuno_group_meta_box()
{
 global $MetaActivities;
 return $MetaActivities->add_yuno_group_meta_box();
}

function show_yuno_group()
{
  global $MetaActivities;
  return $MetaActivities->show_yuno_group();
 
}

add_action('add_meta_boxes', [$MetaActivities, 'add_yuno_group_meta_box']);


add_action('save_post', [$MetaActivities, 'save_yuno_group_meta']);


/************************************************************************
 *********** Start: Enrolled  Group Classes when Group updated************
 *************************************************************************/
add_action('edit_user_profile_update', [$UsersActivities, 'update_extra_profile_fields'], 11, 2);

add_action('handle_bulk_actions-users', [$UsersActivities, 'wcs_bulk_grant_admin_callback'], 10, 3);


function yuno_can_assign($user_id = 0, $taxonomy = '')
{
  global $UsersActivities;
  return $UsersActivities->yuno_can_assign($user_id , $taxonomy );
}


/************************************************************************
 *********** Start: Class instrustor and Date update with user ************
 *************************************************************************/

//add_action( 'save_post', [$ClassRelatedActivities, 'enroll_instructor_classes']);



/***************************************************************************
 **************************** Start: Zoom cron   ****************************
 ****************************************************************************/
function getBatchesFromProductId($p_id)
{
  global $BatchActivities;
  return $BatchActivities->getBatchesFromProductId($p_id);
}

function get_date_diff($time1, $time2, $precision = 2)
{
  global $Utility;
  return $Utility->get_date_diff($time1, $time2, $precision );
}

add_action('after_publish_update_class', [$ClassRelatedActivities, 'after_publish_update_class'], 10, 1);

// This function will run once the 'call_zoom_api_after_publish_update_class' is called  
// schedule_zoom_event runs when a Post is Published
add_action('111publish_tribe_events879', [$TribeEventsActivities, 'update_event_date']);

add_action('wp_ajax_yuno-google-authorization', [$GoogleAuth, 'yuno_google_authorization']);
add_action('wp_ajax_nopriv_yuno-google-authorization', [$GoogleAuth, 'yuno_google_authorization']);

add_action('wp_ajax_set-session-batch-data', [$BatchActivities, 'set_session_batch_data_callback']);
add_action('wp_ajax_nopriv_set-session-batch-data', [$BatchActivities, 'set_session_batch_data_callback']);

/***************************************************************/

// display custom admin notice

add_action('admin_notices', [$InstructorsActivities, 'display_instructor_name_with_id_admin_notice']);




// Disable autoptimize on all pages with the words "term1" or "term2" in the URL
add_filter('autoptimize_filter_noptimize', [$Utility, 'my_ao_noptimize'], 10, 0);

function our_global_exception_handler($exception)
{
  global $Utility;
  return $Utility->our_global_exception_handler($exception);
}

set_exception_handler('our_global_exception_handler');


/*********** our results custom post type ****************/


add_action('init', [$InitPostType, 'result_post_type_callback']);

/*Custom Post type end*/

/********* student results meta box ************/

//making the meta box (Note: meta box != custom meta field)

add_action('add_meta_boxes', [$MetaActivities, 'yuno_result_metabox']);


//showing custom form fields
function student_results_metabox_callback()
{
  global $MetaActivities;
  return $MetaActivities->student_results_metabox_callback();

}

add_action('save_post', [$MetaActivities, 'studentresult_save_meta_fields']);
add_action('new_to_publish', [$MetaActivities, 'studentresult_save_meta_fields']);

function get_timeago($ptime)
{
  global $Utility;
  return $Utility->get_timeago($ptime);
}


/************** post column for product post type ***************/

// Add the custom columns to the book post type:
add_action('init', [$ZohoCrm, 'updateZohoContactAndVendor']);
// add_action('wp_generate_zoho_analytics_access_token', [$ZohoCrm, 'generate_zoho_analytics_access_token']);
// add_action('wp_import_data_in_zoho_analytics', [$ZohoCrm, 'import_data_in_zoho_analytics']);

// Add the data to the custom columns for the book post type:


/**
 * Different style date difference, it is copy of above function
 */
function get_date_diff_for_rest_api($time1, $time2, $precision = 2)
{
  global $Utility;
  return $Utility->get_date_diff_for_rest_api($time1, $time2, $precision );
}

function del_cookie_browse()
{
  global $CookiesActivities;
  return $CookiesActivities->del_cookie_browse();
}
//add_action('init', 'del_cookie_browse');

/* Batch Admin Menu */

add_action('admin_menu', [ $AdminMenu, 'my_admin_menu'] );
function my_admin_page_contents()
{
  global $BatchActivities;
  return $BatchActivities->my_admin_page_contents();
}

add_action('admin_head', [$WpHead, 'my_custom_fonts']);
/* Enrollment Admin Menu */
add_action('admin_menu', [ $AdminMenu,  'my_admin_menu_enrollment'] ,);
function my_admin_page_contents_enrollment()
{
global $EnrollmentActivities;
return $EnrollmentActivities->my_admin_page_contents_enrollment();
  // require("templates/enrollment-list-template.php");
}

add_action('admin_head', [$WpHead, 'my_custom_fonts']);

if (!function_exists('pr')) {
  // global $Utility;
  // return $Utility->pr($var);
  function pr($var)
  {
    echo '<pre>';
    print_r($var);
    echo '</pre>';
  }
}

/**
 * Function that groups an array of associative arrays by some key.
 * 
 * @param {String} $key Property to sort by.
 * @param {Array} $data Array that stores multiple associative arrays.
 */
if (!function_exists('group_by')) {
  function group_by($key, $data)
  {
    $result = array();
    foreach ($data as $val) {
      if (array_key_exists($key, $val)) {
        $result[$val[$key]][] = $val;
      }
      else {
        $result[""][] = $val;
      }
    }
    return $result;
  }
  // global $Utility;
  // return $Utility->group_by($key, $data);
}

// add_action('create-vendors-from-lead-for-crm', [$ZohoCrm, 'createLeadToVendorsForCRM']);


// function createUserInfo($user, $user_data)
// {
//   global $ZohoCrm;
// return $ZohoCrm->createUserInfo($user, $user_data);
// }


add_action('wp_ajax_generate-payment-link-validate', [$PaymentsActivities, 'generate_payment_link_validate_callback']);
add_action('wp_ajax_nopriv_generate-payment-link-validate', [$PaymentsActivities, 'generate_payment_link_validate_callback']);


function create_enrollment($zoho_deal_id, $uId, $productCode, $zohoProductId, $batchName, $update = 0, $payment_status = 'SUCCESS')
{
  global $enrollments;
  return $enrollments->create_enrollment($zoho_deal_id, $uId, $productCode, $zohoProductId, $batchName, $update , $payment_status);
}

/**
 * Add Offline Enrollment menu in admin section in left menu bar
 */

add_action('admin_menu', [$AdminMenu, 'my_admin_menu_offline_enrollment'] );
function my_admin_page_offline_enrollment_contents()
{
  global $EnrollmentActivities;
  return $EnrollmentActivities->my_admin_page_offline_enrollment_contents();
  // require("templates/offline-enrollment-template.php");
}

/**
 * Offline Enrolled learner listing menu in admin section in left menu bar
 */

add_action('admin_menu', [$AdminMenu, 'my_admin_menu_offline_enrolled_list'] );
function my_admin_page_offline_enrolled_list_contents()
{
  global $EnrollmentActivities;
  return $EnrollmentActivities->my_admin_page_offline_enrolled_list_contents();
}

/**
 * Add Offline Enrollment menu in admin section in left menu bar
 */

add_action('admin_menu', [$AdminMenu, 'my_admin_menu_csv_create_batch'] );
function my_admin_page_csv_create_batch_contents()
{
  global $BatchActivities;
  return $BatchActivities->my_admin_page_csv_create_batch_contents();
}

/**
 * Create group menu in admin section in left menu bar
 */

add_action('admin_menu', [$AdminMenu, 'my_admin_menu_create_group'] );
function my_admin_page_create_group()
{
  global $Utility;
  return $Utility->my_admin_page_create_group();
  // require("templates/create-group-template.php");
}


function create_enrollment_webhook($zoho_deal_id, $uId, $productCode, $zohoProductId, $batchName, $update = 0)
{
  global $EnrollmentActivities;
  return $EnrollmentActivities->create_enrollment_webhook($zoho_deal_id, $uId, $productCode, $zohoProductId, $batchName, $update );
}


function payment_data_update_after_webhook_event($event_data, $enrollment_payment_status = 'SUCCESS')
{
  global $PaymentsActivities;
  return $PaymentsActivities->payment_data_update_after_webhook_event($event_data, $enrollment_payment_status);
}

function postMyLearners($my_learners)
{
  global $LearnersActivities;
  return $LearnersActivities->postMyLearners($my_learners);
}

function updatePaymentResponse($payment_obj)
{
  global $PaymentsActivities;
  return $PaymentsActivities->updatePaymentResponse($payment_obj);
}
function updateOrgPaymentResponse($payment_obj)
{
  global $PaymentsActivities;
  return $PaymentsActivities->updateOrgPaymentResponse($payment_obj);
}

/**
 * [Notification settings]
 */
function postNotificationSettings($settings_obj)
{
  global $Notifications;
  return $Notifications->postNotificationSettings($settings_obj);
}


/**
 * Getting current status of user's notifications channels and event name/item
 * Three params are using: 
 * @$user_id = User id
 * @$channel = Channel name would be like whatsapp, email, sms, web, push
 * @$item = Event name
 */
function get_notification_status($user_id, $channel, $item)
{
  global $Notifications;
  return $Notifications->get_notification_status($user_id, $channel, $item);
}
/**
 * [sendNotification for Send whatsApp message on events]
 */
function sendNotification($notification_obj, $channel = null, $item = null)
{
  global $Notifications;
  return $Notifications->sendNotification($notification_obj, $channel = null, $item = null);
}

/**
 * Send whatsapp messages
 */
function sendNotificationNew($notification_obj, $channel = null, $item = null)
{
  global $Notifications;
  return $Notifications->sendNotificationNew($notification_obj, $channel = null, $item = null);
}

/**
 * Delete recordings from vimeo.
 */

add_action('wp_delete_vimeo_video', [$ZoomActivities, 'DeleteVimeoVideo']);
/**
 * get classes
 * get larners fro wp_enrolled
 * 
 * Getting all learners classes for next 2 hours
 * insert value in table 
 * Whatsapp notification send to learner before 2 hours
 */

// add_action('wp_whatsapp_notification_two_hours_before', [$Notifications, 'whatsapp_notification_two_hours_before'] );

/**
 * get classes
 * get larners fro wp_enrolled
 * 
 * Getting all learners classes for next 1 hour
 * insert value in table 
 * Email notification send to only instructors before 1 hour
 */


//add_action('wp_email_notification_one_hour_before', [$Notifications, 'email_notification_one_hour_before'] );

/**
 * Whatsapp notification send to learner before 10 minutes
 */


// add_action('wp_whatsapp_notification_ten_minutes_before', [$Notifications, 'whatsapp_notification_ten_minutes_before'] );


add_action('wp_instructor_notification_ten_minutes', [$Notifications, 'instructor_notification_ten_minutes']);
/**
 * New function create for instructor notification
 * it will handle email and push notifications 
 * One hour before class start
 */

add_action('wp_instructor_notification_one_hour', [$Notifications, 'instructor_notification_one_hour']);

/** 
 * Hook into the 'init' action so that the function
 * Containing our post type registration is not 
 * unnecessarily executed. 
 */

add_action('init', [$InitPostType, 'custom_post_type_courses'], 0);

/**
 * Enable category and tags to custom post type zoho_products (courses)
 */
/*function add_tags_categories() {
register_taxonomy_for_object_type('category', 'course');
register_taxonomy_for_object_type('post_tag', 'course');
}
add_action('init', 'add_tags_categories');*/
// Let us create Taxonomy for Custom Post Type
add_action('init', [$CourseActivities, 'create_course_custom_taxonomy'], 0);



/**
 * Create custom taxonomy
 * Name: Question Bank
 */


add_action('init', [$TaxonomyActivities, 'create_question_bank_custom_taxonomy'], 0);


/**
 * URL re-writing for open page
 */
add_action('init', [$UrlActivities, 'open_init']);

/**
 * URL re-writing for referral page
 */
add_action('init', [$ReferralCodeActivities, 'referral_init']);

/**
 * URL re-writing for resource page
 */
add_action('init', [$UrlActivities, 'resource_init']);

add_filter('query_vars', [$ReferralCodeActivities, 'referral_query_vars']);

add_filter('query_vars', [$UrlActivities, 'open_query_vars']);


/**
 * URL re-writing for invite page
 */
add_action('init', [$UrlActivities, 'invite_init']);

add_filter('query_vars', [$UrlActivities, 'invite_query_vars']);

/**
 * Getting instructor id for profile page
 * return instructor id on the basis of username
 */
add_filter('get_instructor_ID', [$UsersActivities, 'getting_id_by_username']);


add_filter('getting_instructor_ID', [$InstructorsActivities, 'getting_profile_user_id']);

/**
 * Getting parent course id
 * return parent course id on the basis of uri/slug
 */
add_filter('get_parent_course_ID', [$CourseActivities, 'getting_parent_course_id']);

/**
 * Getting category id of category
 * return category id of category on the basis of post id
 */
add_filter('get_category_id_of_category', [$TaxonomyActivities, 'getting_category_id_of_category']);


add_action('wp_head', [$WpHead, 'get_org_id']);
add_action('wp_head', [$WpHead, 'get_class_id']);

/**
 * Getting category of course
 * return course category on the basis of course id
 */
add_filter('get_category_of_course', [$CourseActivities, 'getting_category_of_course']);


/**
 * Getting URI for set state for google login auth
 */
add_filter('set_state', [$UrlActivities, 'setting_state_value']);


/**
 * Getting auth url for google login
 */
function get_auth_url($uri = '/')
{
  global $GoogleAuth;
  return $GoogleAuth->get_auth_url($uri = '/');
}

/**
 * Direct batch payment from course detail page
 */
function batch_front_payment($batchData)
{
  global $BatchActivities;
  return $BatchActivities->batch_front_payment($batchData);
}

/**
 * Custom post type: course
 * When we save custom post type and update custom post type
 * course will be added or updated to zoho crm
 */

add_action('course_add_update_crm', [$CourseActivities, 'course_add_update_crm'], 100, 4);


//add_action( 'save_post_course', [$CourseActivities, 'call_after_save_update_course'] );
add_action( 'acf/save_post', [$CourseActivities, 'call_after_save_update_course'] );

add_filter('wp_insert_post_data', [$CourseActivities, 'filter_post_data'], '99', 2);

/**
 * to display error message to wp-admin, if course has active batches
 */
add_filter('post_updated_messages', [$CourseActivities, 'post_updated_messages_action']);

/**
 * Delete course from zoho crm after delete course from wp-admin
 */
add_action('trash_course', [$CourseActivities, 'course_delete_crm'], 10, 2);

/**
 * Creating a function to create our CPT Ebooks
 */

/**
 * Disable sub menu items
 * Hide sidebar link
 * Hide link on listing page , .tablenav
 */
add_action('admin_menu', [$AdminMenu, 'disable_new_posts']);

// Remove tags support from posts

add_action('init', [$TaxonomyActivities, 'myprefix_unregister_tags']);
/** 
 * Hook into the 'init' action so that the function
 * Containing our post type registration is not 
 * unnecessarily executed. 
 */


add_action('init', [$InitPostType, 'custom_post_type_ebooks'], 0);
add_action('init', [$InitPostType, 'custom_post_type_documents'], 0);
add_action('init', [$InitPostType, 'custom_post_type_user_profile'], 0);
add_action('init', [$InitPostType, 'custom_post_type_org'], 0);
add_action('init', [$InitPostType, 'custom_post_type_orgadminprofile'], 0);
add_action('init', [$InitPostType, 'custom_post_type_referrer_code'], 0);
add_action('init', [$InitPostType, 'custom_post_type_article'], 0);
add_action('init', [$InitPostType, 'custom_post_type_batch'], 0);
add_action('init', [$InitPostType, 'custom_post_type_report'], 0);

/** 
 * Hook into the 'init' action so that the function
 * Containing our post type registration is not 
 * unnecessarily executed. 
 */
add_action('init', [$InitPostType, 'custom_post_type_webhook'], 0);
add_action('init', [$InitPostType, 'custom_post_type_video'], 0);
add_action('init', [$InitPostType, 'custom_post_type_video_testimonial'], 0);
add_action('init', [$InitPostType, 'custom_post_type_exam_result'], 0);
add_action('init', [$InitPostType, 'custom_post_type_learning_content'], 0);


/**
 * After every release, asset_version value should be increased by 1
 */
prevent_browser_caching(
  array(
    'assets_version' => '3.5.927'
  ));

add_action('generate_rewrite_rules', [$UrlActivities, 'mu_custom_posts_add_rewrite_rules']);

/**
 * URL Rewrite rule for blogs 
 */

add_action('generate_rewrite_rules',[$UrlActivities, 'blog_custom_url_rewrite_rules']);


add_filter('post_link', [$UrlActivities, 'my_custom_posts_change_blog_links'], 1, 3);

/**
 * Providing current ebook id to yuno ebook template
 */
add_action('wp_head', [$WpHead, 'get_ebook_id']);


/**
 * Providing current report id to yuno report template
 */
add_action('wp_head', [$WpHead, 'get_report_id']);


/**
 * Providing current article id to yuno article template
 */
add_action('wp_head', [$WpHead, 'get_article_id']);


/**
 * Providing current ebook id to yuno ebook template
 */
add_action('wp_head', [$WpHead, 'get_video_id']);

/**
 * Providing current ebook id to yuno ebook template
 */
add_action('wp_head', [$WpHead, 'get_profile_id']);

/**
 * Providing current learning content id to yuno learning-content template
 */
add_action('wp_head', [$WpHead, 'get_learning_content_id']);


/**
 * Providing current webinar id to yuno webinar template
 */
add_action('wp_head', [$WpHead, 'get_webinar_id']);

/**
 * Providing current video testimonial id to yuno video testimonial template
 */
add_action('wp_head', [$WpHead, 'get_video_testimonial_id']);

/**
 * Providing current exam result id to yuno exam result template
 */
add_action('wp_head', [$WpHead, 'get_exam_result_id']);


/**
 * Get zoho category id from table
 */
function getZohoCategoryId($category)
{
  global $ZohoCrm;
  return $ZohoCrm->getZohoCategoryId($category);
}

/**
 * Getting category from guest url or invite url
 * return category slug
 */
add_filter('get_category_guest_invite', [$TaxonomyActivities, 'getting_category_from_uri']);



/**
 * insert_data_zoho_analytics function is calling 
 * from getting_agents_and_call_logs_from_knowlarity
 * Import json file call logs data into zoho analytics
 */
function insert_data_zoho_analytics($fileName)
{
  global $ZohoCrm;
  return $ZohoCrm->insert_data_zoho_analytics($fileName);
}

/**
 * Getting agent list and call logs from knowlarity.
 * Create json file of call logs
 * import json file to zoho analytics once in a day (night 00:15 am)
 */
add_action('wp_getting_agents_and_call_logs_from_knowlarity', [$LogsActivities, 'getting_agents_and_call_logs_from_knowlarity']);

/**
 * Direct user enrollment in class from unbounce page
 */
function direct_user_enrollment_in_class($class_id, $user_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->direct_user_enrollment_in_class($class_id, $user_id);
}
/**
 * Define logged in user id
 */

add_action('init', [$WpHead, 'hf_Function']);


/**
 * get cache
 */
function get_cache($key)
{
  global $CacheActivities;
  return $CacheActivities->get_cache($key);
}

/**
 * add cache
 * $key = cache name
 * $value = it can be string / array
 * $group = multiple records save in row
 * $expire = time in seconds (1 hour = 3600)
 */
function add_cache($key='', $value='', $group = '', $expire='')
{
  global $CacheActivities;
  return $CacheActivities->add_cache($key , $value , $group , $expire );
}

/**
 * delete cache
 */
function delete_cache($key = '', $group = '', $ids = [])
{
 global $CacheActivities;
 return $CacheActivities->delete_cache($key , $group , $ids);
}

/**
 * flush cache
 */

add_action('wp_flush_all_cache', [$CacheActivities, 'flush_cache']);

/**
 * Email notifiation send on event basis
 */
function email_notification($email_type, $user_id, $classData = '')
{
  global $Notifications;
  return $Notifications->email_notification($email_type, $user_id, $classData);
}

/**
 * Getting learners total attended time per class
 * import records to elastic search once in a day (night 00:15 am)
 */
function getting_learners_total_attended_time_per_class($class_id, $limit)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->getting_learners_total_attended_time_per_class($class_id, $limit);
}

// /**
//  * update enrollment status after crossed the end date into db
//  */
// //if( !wp_next_scheduled( 'update_enrollment_status_after_crossed_the_end_date_into_db' ) ) {
// //wp_schedule_event(time(), '1min', 'update_enrollment_status_after_crossed_the_end_date_into_db' );
// //wp_schedule_event(time(), 'daily', 'update_enrollment_status_after_crossed_the_end_date_into_db' );

add_action('wp_update_enrollment_status_after_crossed_the_end_date_into_db', [$EnrollmentActivities, 'update_enrollment_status_after_crossed_the_end_date_into_db']);
// }
/**
 * Google authentication check for web client id
 */
function google_auth_check($token)
{
  global $GoogleAuth;
  return $GoogleAuth->google_auth_check($token);
}


/**
 * Token validation check for wp rest apis
 */
function token_validation_check($token = '')
{
  global $TokenActivities;
  return $TokenActivities->token_validation_check($token);
}

/**
 * This function will execute if token got expired in sleep mode for web only
 * or execute from header on refresh
 */
function web_token_refresh_call($token)
{
  global $TokenActivities;
  return $TokenActivities->web_token_refresh_call($token);
}

/**
 * Insert data in elastic search of notification
 */
function notification_data_insert($key, $data)
{
 global $Notifications;
  return $Notifications->notification_data_insert($key, $data);
}
/**
 * It is to display all resource items under single menu in wp-admin
 */

add_action('admin_menu', [$AdminMenu, 'wpse_226690_admin_menu']);
/**
 * Providing current custom post type custom url
 */

add_filter('post_type_link', [$UrlActivities, 'prefix_filter_news_permalink'], 10, 2);
/**
 * Generate isPolicyRegisteredForInsights callback action
 */
function isPolicyRegisteredForInsights()
{
  global $UsersActivities;
  return $UsersActivities->isPolicyRegisteredForInsights();
}
/**
 * Getting agent list and call logs from knowlarity.
 * Post user_called_event of call logs to ES
 * post data to ES once in an hour (night/day per hour)
 */

add_action('wp_user_called_event', [$UsersActivities, 'user_called_event']);

/**
 * Custom post type: tribe_events
 * When we save custom post type and update custom post type
 * tribe_events will be added or updated to ES
 */

function tribe_events_add_update_es($post_id)
{
  global $ElasticSearch;
  return $ElasticSearch->tribe_events_add_update_es($post_id);
}
add_action('tribe_events_add_update_es', [$ElasticSearch, 'tribe_events_add_update_es'], 10, 1);

/**
 * Calls the tribe_events_add_update_es function after a tribe event post is saved or updated.
 *
 * @param int $post_id The ID of the post being saved.
 */
/*function call_after_save_update_tribe_events($post_id) {
    // Calls a custom function to handle the update in the tribe events.
    tribe_events_add_update_es($post_id);
}

// Hooks the 'call_after_save_update_tribe_events' function to the 'save_post_tribe_events' action,
// so that it will be called when a post of type 'tribe_events' is saved or updated.
add_action('save_post_tribe_events', 'call_after_save_update_tribe_events', 10, 1);
*/
/**
 * convert html into jpg
 */
function convert_html_into_jpg($params)
{
  global $WpHead;
  return $WpHead->convert_html_into_jpg($params);
}
/**
 * filter bookmark cache
 */
function filter_bookmark_of_resource_item_of_user($user_id, $resource_id)
{
  global $UsersActivities;
  return $UsersActivities->filter_bookmark_of_resource_item_of_user($user_id, $resource_id);
}
/**
 * Get category of post/CPT (course, ebook, document etc...)
 * $id would be post or CPT id
 */
function get_post_category($id, $flag = '')
{
  global $TaxonomyActivities;
  return $TaxonomyActivities->get_post_category($id, $flag );
}
/**
 * Update status of user notification channels and status of channel's event for a user
 * Updation is in json file (Not in Database)
 * Four params are using: 
 * @$user_id = User id
 * @$status = true/false
 * @$channel = Channels name would be whatsapp, email, push, web, sms
 * @$item = Default value is NULL, Item is an event name
 */
function update_user_notifications($user_id, $status, $channel, $item = null)
{
  global $Notifications;
  return $Notifications->update_user_notifications($user_id, $status, $channel, $item = null);
}

/**
 * Insert default notification settings to json file
 */
function insert_notification_old($user_id)
{
  global $Notifications;
  return $Notifications->insert_notification_old($user_id);
}

/** bulk insertion for user default notification settings -- reolacement of old insert notification **/
function insert_notification($user_id)
{
  global $Notifications;
  return $Notifications->insert_notification($user_id);
}

/**
 * This function is replacement of script for add user notification into json file
 * This function will execute after login
 * Then check if user id exist in notification json file then leave it 
 * otherwise create notification for this particular user
 * Call once at the time of login
 */
function add_user_notification($user_id)
{
  global $Notifications;
  return $Notifications->add_user_notification($user_id);
}

/**
 * Getting count for existing username
 * concat count + 1 after username
 */
function customUsernameCreate($yuno_user_name)
{
  global $UsersActivities;
  return $UsersActivities->customUsernameCreate($yuno_user_name);
}
/**
 * Delete ebook from es after delete ebook from wp-admin
 */
add_action('trash_ebooks', [$ElasticSearch, 'ebooks_delete_es'], 10, 2);

/**
 * Delete document from es after delete document from wp-admin
 */
add_action('trash_documents', [$ElasticSearch, 'documents_delete_es'], 10, 2);

/**
 * Delete video from es after delete video from wp-admin
 */
add_action('trash_video', [$ElasticSearch, 'video_delete_es'], 10, 2);


/**
 * Insert upcoming notification data for 2 hour insert in our temporal table
 */

add_action('wp_insert_upcoming_notification', [$Notifications, 'insert_upcoming_notification'] );

/**
 * Insert ten minutes upcoming notification ino our temporal table
 */


add_action('wp_insert_ten_minutes_upcoming_notification', [$Notifications, 'insert_ten_minutes_upcoming_notification']);

/**
 * Whatsapp temporal notifications send
 */

add_action('wp_whatsapp_temporal_notifications', [$Notifications, 'whatsapp_temporal_notifications']);

/**
 * Email temporal notifications send
 */

add_action('wp_email_temporal_notifications', [$Notifications, 'email_temporal_notifications']);

/**
 * Push temporal notifications send
 */


add_action('wp_push_temporal_notifications', [$Notifications, 'push_temporal_notifications']);

/**
 * Delete previous day notification from our table
 */


add_action('wp_delete_notification_old_record', [$Notifications, 'delete_notification_old_record']);

/**
 * Update with all resources include update delete and insert
 * File name is resources_data.json
 * I am doing this because of, i want zoho analytics up-to-date everyday
 */
function update_resource_data_file($collection)
{
  global $Utility;
  return $Utility->update_resource_data_file($collection);
}

/**
 * get all resources from elastic search 
 * setup cron at night 00:15 
 */

add_action('wp_get_all_resources_from_elastic_search', [$ElasticSearch, 'get_all_resources_from_elastic_search']);

/**
 * User role: learner
 * When we save learner notification data and update in es
 * learner will be added or updated to es
 */
add_action('update_user_notifications_es', [$ElasticSearch, 'update_user_notifications_es'], 100, 4);
function update_user_notifications_es($user_id)
{
  global $ElasticSearch;
  return $ElasticSearch->update_user_notifications_es($user_id);

}


/**
 * User role: learner
 * When we save learner notification data and update in es
 * learner will be added or updated to es
 */

add_action('user_register', [$ElasticSearch, 'post_user_notifications_es'], 10, 1);

/**
 * Get campaign audience from elastic search
 * function is calling from campaign controller
 */
function get_campaign_audience($category)
{
global $UsersActivities;
return $UsersActivities->get_campaign_audience($category);
}

/**
 * User role: all
 * When we save user agent and update in interaction data
 * user agent will be added to interaction data
 */
function detect_and_indentify_user_agent($type = null)
{
  global $UsersActivities;
  return $UsersActivities->detect_and_indentify_user_agent($type);
}

/**
 * Create json for segment
 * save json
 * entry in database for segment
 * copy file to bucket
 */
function create_json_for_segment($users, $user_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->create_json_for_segment($users, $user_id);
}
/**
 * update learner insights
 * 
 */

add_action('wp_update_learner_insights_attendance', [$LearnersActivities, 'update_learner_insights_attendance']);

/**
 * Checking private users or learners are enrolled in class or not
 */
function user_enrollment_check_in_class($event_ID, $user_ID)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->user_enrollment_check_in_class($event_ID, $user_ID);
}

/**
 * Class schedule and update tasks, that should happen after creat class
 * $userID = Instructor ID
 */
function class_schedule_tasks($Classlearner = [], $EventID='', $ClassTitle='', $ClassExcerpt='', $evantStartDate='', $evantEndDate='', $classLearnerEmails = [], $classCourseCategory='', $http_user_agent='', $userID='', $ClassLink='', $dayOfWeek='', $ClassStartDate='', $ClassStartTimeForWhatsApp='', $instructorName='')
{
 global $ClassRelatedActivities;
  return $ClassRelatedActivities->class_schedule_tasks($Classlearner , $EventID , $ClassTitle , $ClassExcerpt , $evantStartDate , $evantEndDate , $classLearnerEmails , $classCourseCategory , $http_user_agent , $userID , $ClassLink , $dayOfWeek , $ClassStartDate , $ClassStartTimeForWhatsApp , $instructorName);
}

/**
 * This hook will execute after class update
 */

// add_action('wp_update_class_execute_tasks', [$ClassRelatedActivities, 'class_update_tasks'], 10, 16);

/**
 * Delete class scheduler hook
 */

// add_action('wp_class_delete_tasks', [$ClassRelatedActivities, 'class_delete_tasks'], 10, 3);

/**
 * For the data
 * get query -- records -- pending -- with limit 1 order by ASC
 * curl request -- update csv -- that is already created with queue id --
 * update query with condition 
 * create wp scheduler
 */
function pending_csv_record_process()
{
  global $CsvActivities;
  return $CsvActivities->pending_csv_record_process();
}

/**
 * CSV processing
 * */
function csv_process($new_endpoint, $filename, $file_action)
{
  global $CsvActivities;
  return $CsvActivities->csv_process($new_endpoint, $filename, $file_action);
}


/**
 * User role: all
 * When we delete user notification settings in es
 * 
 */
function delete_user_notification_settings_documents($user_id)
{
  global $Notifications;
  return $Notifications->delete_user_notification_settings_documents($user_id);
}

/**
 * post user notification setting
 */

add_action('wp_notification_setting', [$Notifications, 'notification_setting']);

/**
 * 
 * update not launch classes
 */

add_action('wp_update_not_launch_classes', [ $ClassRelatedActivities, 'update_not_launch_classes']);

/**
 * get zoom class report per class
 */
function get_zoom_report_per_class($class_id, $learner_id)
{
  global $ZoomActivities;
  return $ZoomActivities->get_zoom_report_per_class($class_id, $learner_id);
}
/**
 * Calculate batch completion percentage
 */
function batch_completion_percentage($batch_start_date_time, $batch_end_date_time)
{
  global $BatchActivities;
  return $BatchActivities->batch_completion_percentage($batch_start_date_time, $batch_end_date_time);
}
/**
 * Get active batches count of course
 */
function get_active_batches_count($course_id, $instructor_id)
{
  global $BatchActivities;
  return $BatchActivities->get_active_batches_count($course_id, $instructor_id);
 
}
/**
 * Get active enrollments count of course
 */
function get_active_enrollments($course_id, $instructor_id)
{
    global $EnrollmentActivities;
    return $EnrollmentActivities->get_active_enrollments($course_id, $instructor_id);   
}
/**
 * Get active live classes of course
 * SELECT * FROM wp_postmeta as t1 left join wp_postmeta as t2 on (t1.post_id = t2.post_id and t2.meta_key='Yuno_Class_Related_Courses') WHERE (t2.meta_value REGEXP '[[:<:]]9139[[:>:]]') 
 */
function get_active_live_classes($course_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->get_active_live_classes($course_id);
}

/**
 * Checking user is exists in system or not
 * wheather it is belongs to any role
 * Param 1: $user_id
 * Param 2: $role
 * return boolean
 */
function check_user_existance($user_id, $role = '')
{
  global $UsersActivities;
  return $UsersActivities->check_user_existance($user_id, $role );

}

/**
 * batch event per class call
 */
function learners_batch_attended_time_per_class($class_id, $limit)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->learners_batch_attended_time_per_class($class_id, $limit);
}

/**
 * past sentry missing class recording per class call
 */
function missing_past_class($class_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->missing_past_class($class_id);  
}

/**
 * past class recording per class call
 */
function past_class_recording_per_class($class_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->past_class_recording_per_class($class_id);  
}

/**
 * To add feedback information into ES document
 */

add_action("added_post_meta", [$ElasticSearch, 'add_feedback_status_es'], 10, 4);
/**
 * Get available seats from total available seats in batch
 */
function get_batch_available_seats($batch_reference_post_id, $batch_id, $course_id)
{
  global $BatchActivities;
  return $BatchActivities->get_batch_available_seats($batch_reference_post_id, $batch_id, $course_id);
}

/**
 * Providing current env course_category taxonomy infrmation to yuno popular api
 */
add_action('init', [$CourseActivities, 'get_course_category_info']);


/***
 * to update the keys in the ES document for Instrutor Stats
 * */

add_action('wp_update_es_keys_instructor_stats', [$ElasticSearch, 'update_es_keys_instructor_stats']);

/**
 * Get days from two dates
 */
function get_days_from_dates($date1, $date2)
{
  global $Utility;
  return $Utility->get_days_from_dates($date1, $date2);
}

/**
 * Creating a function to create our CPT course_schedule
 */

add_action('init', [$InitPostType, 'custom_post_type_course_schedule'], 0);

/**
 * Get instructor detail from ES document - instructorsignedup
 * completed enrollments
 */
function get_instructor_data($instructor_id)
{
 global $InstructorsActivities;
 return $InstructorsActivities->get_instructor_data($instructor_id);
}

/**
 * get instructor rating and review from existing api
 */
function get_instructor_detail($instructor_id)
{
 global $InstructorsActivities;
 return $InstructorsActivities->get_instructor_detail($instructor_id);
}

/** cron only once 01:00 am
 * Custom post type: tribe_events
 * When we done the class
 * enroll/ attend counter will be added or updated to es
 */

add_action('wp_enroll_attend_after_class_done', [$ClassRelatedActivities, 'enroll_attend_after_class_done']);

/** cron run every 1 hour
 * post enroll/ attend counter es
 */

add_action('wp_post_enroll_attend_count_es', [$ElasticSearch, 'post_enroll_attend_count_es']);

/**
 * 
 * update enrollment and attandance counter in privateclass and webinar es docs for past classes api
 */
function update_enroll_attend_count_classes($request)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->update_enroll_attend_count_classes($request); 
}

/**
 * To update the keys in the ES document for Mapped Courses, Mapped Categories
 */
function update_es_mapped_courses_categories_instructor($data)
{
  global $ElasticSearch;
  return $ElasticSearch->update_es_mapped_courses_categories_instructor($data);
}

/***
 * To update the keys in the ES document for Average Rating
 * */

add_action('wp_update_es_mapped_rating_instructor', [$ElasticSearch, 'update_es_mapped_rating_instructor']);

/**
 * filter user's org status
 */
function filter_org_of_user($user_id, $org_id)
{
 global $OrgActivities;
 return $OrgActivities->filter_org_of_user($user_id, $org_id);
}

/**
 * update org users object
 */
function update_org_users_object()
{
 global $OrgActivities;
 return $OrgActivities->update_org_users_object();
}

/**
 * update batch enrollment status as active/inactive
 */
function update_batch_enrollment_status_temp($limit)
{
  global $BatchActivities;
  return $BatchActivities->update_batch_enrollment_status_temp($limit);
}

/**
 * update batch enrollment status as active/inactive
 */
function update_batch_enrollment_status($limit)
{
  global $BatchActivities;
  return $BatchActivities->update_batch_enrollment_status($limit);
}

/** cron only once 01:15 am
 * instructor active batch update per course
 * once in a day
 * course batch object will be updated to es
 */

add_action('wp_course_batch_object_update_done', [$CourseActivities, 'course_batch_object_update_done']);

/**
 * update batch enrollment status as active/inactive
 */
function update_course_batch_object($limit)
{
  global $CourseActivities;
  return $CourseActivities->update_course_batch_object($limit);
}

/**
 * To migrate course active enrollments into ES document- script-  CRON Set up
 */

add_action('wp_migrate_course_active_enrollments_script', [$CourseActivities, 'migrate_course_active_enrollments_script']);

/**
 * to become referrer
 * Update referral code of instructor as referrer
 */
function to_become_referrer($user_id)
{
  global $ReferralCodeActivities;
  return $ReferralCodeActivities->to_become_referrer($user_id);
}

/**
 * Processing offline enrollments
 */
function pending_offline_enrollment_process($data)
{
  global $EnrollmentActivities;
  return $EnrollmentActivities->pending_offline_enrollment_process($data);
}
/**
 * to get discounted amount
 * get discount on referral code of instructor as referrer
 */
function to_get_discounted_amount($info)
{
  global $ReferralCodeActivities;
  return $ReferralCodeActivities->to_get_discounted_amount($info);
  
}


/**
 * traceid-enter-{methodname}-linenumber-timestamp
 * $unique_id = Unique id for idenification
 * $method_name = name of the function
 * $line_number = line number from code (but it might be change because 
 * multiple developers are working on same file)
 * $timestamp = current time stamp, $message = ''
 * $filename = name of the file for track error logs - just a file name not extension
 */
function track_logs($unique_id = 'No unique Id', $method_name = 'No method name', $line_number = 'No line number', $timestamp = 'No timestamp', $message = 'No message', $filename = '', $difference_in_miliseconds = '')
{
  global $LogsActivities;
  return $LogsActivities->track_logs($unique_id, $method_name , $line_number , $timestamp , $message , $filename , $difference_in_miliseconds );
}
/**
 * To create question as custom post type
 * */

add_action('init', [$InitPostType, 'custom_post_type_question'], 0);

/**
 * To create course_economics as custom post type
 * */

add_action('init', [$InitPostType, 'custom_post_type_course_economics'], 0);
/**
 * To create question set as custom post type
 * */
add_action('init', [$InitPostType, 'custom_post_type_question_set'], 0);

/**
 * To create quiz attempt as custom post type
 * */
add_action('init', [$Quiz, 'custom_post_type_quiz'], 0);
add_action('init', [$Quiz, 'custom_post_type_quiz_attempt'], 0);



/**
 * Delete older records from wp_user_tokens table and wp_pantheon_sessions table
 * Need to add this on cron on production, 
 * if we face any issue related to this at dev and stage then we need to setup this cron at 
 * dev and on stage as well
 */

//add_action("wp_delete_not_required_data_from_table", [$CronActivities, "delete_not_required_data_from_table"]);

/**
 * Disable feeds
 */
add_action('do_feed', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_rdf', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_rss', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_rss2', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_atom', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_rss2_comments', [$WpHead, 'wp_disable_feeds'], 1);
add_action('do_feed_atom_comments', [$WpHead, 'wp_disable_feeds'], 1);

add_filter( 'rest_url_prefix', [$Utility, 'modify_endpoint_api_slug']);
// function modify_endpoint_api_slug( $slug ) { return 'wp-json'; }

flush_rewrite_rules(true);
/**
 * Creating a function to create our CPT Class Video Clipping
 */


/** 
 * Hook into the 'init' action so that the function
 * Containing our post type registration is not 
 * unnecessarily executed. 
 */

add_action('init', [$InitPostType, 'custom_post_type_class_video_clipping'], 0);

/**
 * Getting category of course in referrer case
 * return course category on the basis of referrer id
 */
add_filter('get_referrer_category_of_course', [$CourseActivities, 'getting_referrer_category_of_course']);

/**
 * Schedule segment event
 * executing under cron which is running every 15 minutes
 * Expected: all segment delayed calls will come in this table and send data to specific destination
 */

add_action('wp_segment_pending_task_process', [$SegmentPostsActivities, 'segment_pending_task_process']);
/**
 * Custom post type: org
 * When we save custom post type and update custom post type
 * org will be added or updated to es
 */
//add_action( 'add_org_es', [$SegmentPostsActivities, 'add_org_es'] , 10, 1 );
// add_action( 'save_post_org', [$SegmentPostsActivities, 'call_after_save_update_org_updated'] , 10, 1 );
//add_action( 'post_updated', [$SegmentPostsActivities, 'call_after_save_update_org_updated'] , 10, 3 );
// add_action( 'save_post_org', [$SegmentPostsActivities, 'add_org_details_es'] , 10, 1 );

add_action('save_post_org', [$SegmentPostsActivities, 'call_after_save_update_org_updated'], 10, 1);

// Register the action hook for the delayed execution function.
add_action('call_after_save_org_meta_updated', [$SegmentPostsActivities, 'handle_delayed_org_meta_update'], 10, 1);

/**
 * To create question as custom post type
 * */

add_action('init', [$InitPostType, 'custom_post_type_question_set2'], 0);

/**
 * Post add enrollment: segment
 * When we save enrollment data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_add_enrollment($enrollment_id,$record_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_add_enrollment($enrollment_id,$record_id);
}
/**
 * Post update enrollment: segment
 * When we save enrollment data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_update_enrollment($enrollment_id,$record_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_update_enrollment($enrollment_id,$record_id);
}
/**
 * Post unenroll learner: segment
 * When we unenroll learner data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_unenroll_learner($enrollment_id,$record_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_unenroll_learner($enrollment_id, $record_id);
}

/**
 * Custom post type: org
 * When we save custom post type and update custom post type
 * org will be added or updated to es
 */

add_action('save_post_examresult', [$SegmentPostsActivities, 'call_after_save_update_examresult'], 10, 1);
/**
 * Post create examresult: segment
 * When we save examresult data it will send data to segment
 * org will be created or updated in it
 */
function segment_post_create_examresult($examresult_id,$record_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_create_examresult($examresult_id,$record_id);
}
/**
 * Post update examresult: segment
 * When we save examresult data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_update_examresult($examresult_id,$record_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_update_examresult($examresult_id, $record_id);
}
/**
 * Post delete examresult: segment
 * When we delete examresult data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_delete_examresult($examresult_id, $record_id = null)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_delete_examresult($examresult_id, $record_id);
}
/**
 * Post delete examresult: segment
 * When we delete examresult data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_delete_examresult_record($examresult_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_delete_examresult_record($examresult_id);
}
/**
 * Post update class reviwes: segment
 * When we save class reviews data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_update_class_reviews($class_id,$user_id,$record_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->segment_post_update_class_reviews($class_id,$user_id,$record_id);
}
/**
 * Post create class reviwes: segment
 * When we save class reviews data it will send data to segment
 * org will be created or updated in it
 */
function segment_post_create_class_reviews($class_id,$user_id,$record_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->segment_post_create_class_reviews($class_id,$user_id,$record_id);
}
/**
 * Post update class attended: segment
 * When we save class attended data it will send data to segment
 * org will be added or updated in it
 */
function segment_post_update_class_attended($class_id,$user_id,$record_id)
{
  global $ClassRelatedActivities;
  return $ClassRelatedActivities->segment_post_update_class_attended($class_id,$user_id,$record_id);
}
//add_action( 'save_post_org', 'call_after_save_update_org' , 10, 1 );
/**
 * it is for integrations key of segment for every call
 * basically it define the no of destinations
 * we can define urls here
 * it will be the only one source to define urls 
 */
function segment_integrations_key()
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_integrations_key();
}

/**
 * Redirection if user logged in
 */

add_action('init', [$Utility, 'collection_redirection']);
/**
 * Custom post type: webhook
 * When we save custom post type and update custom post type
 * webhook will be added or updated to es
 */
function org_webhook_add_update_es($webhook_id)
{
  global $ElasticSearch;
  return $ElasticSearch->org_webhook_add_update_es($webhook_id);
}


/**
 * Custom post type: webhook
 * When we save custom post type and update custom post type
 * webhook will be added or updated to es
 */
add_action( 'save_post_webhook', [$SegmentPostsActivities, 'call_after_save_update_webhook'] , 10, 1 );
/**
 * To get user name and image from signedup es doc
 */
function get_event_status_from_es($org_id)
{
  global $ElasticSearch;
  return $ElasticSearch->get_event_status_from_es($org_id);
}
/**
 * Post add user: segment
 * When we save user data it will send data to segment
 * org will be added or updated in it
 * todo: remove curl discuss with gagan
 */
function segment_post_add_user($user_id)
{
  global $SegmentPostsActivities;
  return $SegmentPostsActivities->segment_post_add_user($user_id);
}
/** * Upload image in wordpress media folders and make image as featured image of post*/
function uploadImage($post_id, $file) {
  global $Utility;
  return $Utility->uploadImage($post_id, $file);
}

/**
 * Custom user meta: es
 * When we save custom user meta and update custom user meta
 * data will be added or updated to es
 */
function user_add_update_es($webhook_id)
{
  global $ElasticSearch;
  return $ElasticSearch->user_add_update_es($webhook_id);
}
/**
 * Getting zoom oauth app token
 * return zoom oauth app token on the basis of user id
 */
add_filter( 'get_zoom_oauth_app_token', [$ZoomActivities, 'getting_zoom_oauth_app_token'] );
function getting_zoom_oauth_app_token()
{ 
  global $ZoomActivities;
  return $ZoomActivities->getting_zoom_oauth_app_token();
}
/**
 * Getting zoom oauth app refresh token cron
 * return zoom oauth app refresh token cron on the basis of user id
 */
function getting_zoom_oauth_app_token_after_expiry($user_id)
{ 
  global $ZoomActivities;
  return $ZoomActivities->getting_zoom_oauth_app_token_after_expiry($user_id);
}
/**
 * Getting zoom oauth app refresh token cron
 * return zoom oauth app refresh token cron on the basis of user id
 */

// add_action('wp_getting_zoom_oauth_app_refresh_token', [$ZoomActivities, 'getting_zoom_oauth_app_refresh_token']);

function yuno_zoom_instructor_state($user_id) {
  global $ZoomActivities;
  return $ZoomActivities->yuno_zoom_instructor_state($user_id);
}


/**
 * Test Cron
 */

add_action('wp_custom_cron_test', [$CronActivities, 'my_custom_cron_job']);


add_action( 'template_redirect', [$WpHead, 'language_redirect'] );
/**
 * common course price calculation in common area,  we will use this action to get pricee of course as string.
 */
function course_price_distribution($course_price, $course_id = null)
{ 
  global $CourseActivities;
  return $CourseActivities->course_price_distribution($course_price, $course_id = null);
}
/**
 * common course price calculation in common area, we will use this action to get pricee of course as string. also it is country based. 
 */
function country_based_course_price_distribution($course_price, $country = null, $course_id = null)
{
  global $CourseActivities;
  return $CourseActivities->country_based_course_price_distribution($course_price, $country , $course_id);
}
/**
 * country course price option table insertion
 */
// add_action('init', [$CourseActivities, 'country_course_price']);

add_action('wp_enqueue_scripts', [$EnqueueScripts, 'remove_specific_css'], 9999);

add_action('wp_enqueue_scripts', [$EnqueueScripts, 'deregister_custom_css'], 9999);

/**
 * common-es-get instructorsignedup doc
 * this function is moved on Elastic-Search.php file
 */
function get_instructorsignedup($case)
{
  global $InstructorsActivities;
  return $InstructorsActivities->get_instructorsignedup($case);
}
/**
 * common-es-get privateclass,webinar doc
 */
function get_privateclass_webinar($case, $data)
{
  global $ClassRelatedActivities; 
  return $ClassRelatedActivities->get_privateclass_webinar($case, $data); 
}

/**
 * To create question as custom post type
 * */

add_action('init', [$InitPostType, 'custom_post_type_question_set3'], 0);

/** custom_tribe_create_update_event
 * it is for alternative of tribe event plugin for every meeting call
 * basically it define the no of postmeta keys
 * we can define more keys here
 * it will be the only one source to define post meta keys 
 */
function tribe_create_event($data)
{
  global $TribeEventsActivities;
  return $TribeEventsActivities->tribe_create_event($data);
}
/** custom_tribe_create_update_event
 * it is for alternative of tribe event plugin for every meeting call
 * basically it define the no of postmeta keys
 * we can define more keys here
 * it will be the only one source to define post meta keys 
 */
function tribe_update_event($post_id,$data)
{
  global $TribeEventsActivities;
  return $TribeEventsActivities->tribe_update_event($post_id, $data);
}

/** tribe_get_start_date
 * it is for alternative of tribe event plugin for every meeting call
 * basically it define the no of postmeta keys
 * we can define more keys here
 * it will be the only one source to define post meta keys 
 */
function tribe_get_start_date($post_id = null, $display_time = true, $date_format = '', $timezone = null)
{
  global $TribeEventsActivities;
  return $TribeEventsActivities->tribe_get_start_date( $post_id , $display_time , $date_format , $timezone );
}
/** tribe_get_start_date
 * This function is movied in the class TribeEventsActivities
 * it is for alternative of tribe event plugin for every meeting call
 * basically it define the no of postmeta keys
 * we can define more keys here
 * it will be the only one source to define post meta keys 
 */
function tribe_get_end_date($post_id = null, $display_time = true, $date_format = '', $timezone = null)
{
  global $TribeEventsActivities;
    return  $TribeEventsActivities->tribe_get_end_date($post_id = null, $display_time = true, $date_format, $timezone = null);
}

add_action('init', [$TribeEventsActivities, 'create_tribe_events_cat_custom_taxonomy'], 0);

add_action('init', [$InitPostType, 'custom_post_type_tribe_events'], 0);

/**
 * Registers a custom post type called "Async Task."
 *
 * This post type is used to manage asynchronous tasks within the WordPress
 * admin interface. It provides various labels for different contexts, such as
 * menus, and specifies other behavior and characteristics of the post type.
 */

// Hooks the 'create_async_task_post_type' function to the 'init' action,
// so that it will be called on WordPress initialization.
add_action('init', [$InitPostType, 'create_async_task_post_type']);

//add_action('save_post_tribe_events', [$TribeEventsActivities, 'call_after_save_update_tribe_events'], 10, 1);

/**
 * post class es
 */

/* for experiment i am commenting add action and removing cron from wp-admin, which is scheduling in every 5 minutes*/

add_action('wp_post_class_es', [$ElasticSearch, 'post_class_es']);
/**
 * Get JWT token of user
 */
function get_jwt_token($user_id) 
{
  global $JwtAuthActivities;
  return $JwtAuthActivities->get_jwt_token($user_id);
}

/**
 * Validate JWT Token
 */
function validate_jwt_token($authToken) 
{
  global $JwtAuthActivities;
  return $JwtAuthActivities->validate_jwt_token($authToken);
}

/**
 * Generate or regenerate JWT token for user
 */
function create_jwt_token($user_id) {

  global $JwtAuthActivities;
  return $JwtAuthActivities->create_jwt_token($user_id);

}

/**
 * Change the token's expire value.
 *
 * @param int $expire The default "exp" value in timestamp.
 * @param int $issued_at The "iat" value in timestamp.
 *
 * @return int The "nbf" value.
 */
//add_filter('jwt_auth_expire', [$JwtAuthActivities, 'jwt_auth_expire_time_set'],10,2 );

/**
 * Getting zoho product id
 */
function getZohoProductId($courseId)
{
  global $ZohoCrm;
  return $ZohoCrm->getZohoProductId($courseId);
}
/**
 * To create question set as custom post type - IELTS Reading True False Not Given
 * */
add_action('init', [$InitPostType, 'cpt_ieltsr_tfng_qset'], 0);
/**
 * To create question set as custom post type - IELTS Reading Yes No Not Given
 * */
add_action('init', [$InitPostType, 'cpt_ieltsr_ynng_qset'], 0);
/**
 * To create question set as custom post type - IELTS Reading Note Completion
 * */
add_action('init', [$InitPostType, 'cpt_ieltsr_nc_qset'], 0);

/**
 * To create question set as custom post type -  Note Completion Question
 * */
add_action('init', [$InitPostType, 'cpt_nc_question'], 0);
/**
 * To create custom post type - Academy
 * */
add_action('init', [$InitPostType, 'cpt_academy'], 0);
/**
 * To logout current user
 * */
add_action('init', [$InitPostType, 'yuno_cognito_logout'], 0);
// Hook the filter to the script_loader_tag action
add_filter('script_loader_tag', [$EnqueueScripts, 'add_defer_attribute'], 10, 2);
/**
 * Creating a function to create our CPT Delete User Requests
 */
add_action('init', [$InitPostType, 'cpt_delete_user_requests'], 0);
/**
 * To logout current user
 * */
add_action('init', [$InitPostType, 'yuno_login_access'], 0);
/**
 * Token jwt sdk validation check for wp rest apis
 */
function jwt_token_validation_check($token = '')
{
  global $TokenActivities;
  return $TokenActivities->jwt_token_validation_check($token);
}
/**
 * This is to detect the web or mobile browser
 */
function is_mobile_web_device()
{
  global $DeviceActivities;
  return $DeviceActivities->mobile_web_device_check();
}
//Ensure WordPress is loaded
// add_action( 'init', function () {
//   $my_background_process = new My_Background_Process();

//   // Add tasks to the queue
//   $my_background_process->push_to_queue( 'Task 1' )
//                         ->push_to_queue( 'Task 2' )
//                         ->push_to_queue( 'Task 3' )
//                         ->save()->dispatch();
// });
function log_cron_events( $event ) {
  $current_day = date('j-m-Y');
  // Path to the log file
  $log_file_path = '/error-logs/cron/cron-logs-'.$current_day.'.csv';
  // Message to log
  $message = sprintf( "[%s], Scheduled cron event, '%s' ,for, %s\n", date( "Y-m-d H:i:s" ), $event->hook, date( "Y-m-d H:i:s", $event->timestamp ) );
  // Append to log file
  @file_put_contents( $log_file_path, $message, FILE_APPEND );
}
//add_action( 'schedule_event', 'log_cron_events' );

function log_cron_unschedule_events( $timestamp, $hook, $instance ) {
  $current_day = date('j-m-Y');
  $log_file_path =  '/error-logs/cron/cron-logs-'.$current_day.'.csv';
  $message = sprintf( "[%s], Unscheduled cron event, ''%s' ,for, %s\n", date( "Y-m-d H:i:s" ), $hook, date( "Y-m-d H:i:s", $timestamp )  );
  @file_put_contents( $log_file_path, $message, FILE_APPEND );
}
//add_action( 'wp_unschedule_event', 'log_cron_unschedule_events', 10, 3 );
// Your code to enqueue parent theme styles
function enqueue_parent_styles() {
  wp_enqueue_style( 'parent-style', get_template_directory_uri() . '/style.css' );
}

add_action( 'wp_enqueue_scripts', 'enqueue_parent_styles' );
/**
 * Updates the custom user meta value for a given user.
 *
 * @param int $user_id The ID of the user.
 * @param mixed $old_user_data The old user data (not used in this function).
 * @return void
 */
function update_custom_user_meta($user_id, $old_user_data)
{
  global $UsersActivities;
  return $UsersActivities->update_custom_user_meta($user_id, $old_user_data);
}
// Hook to update custom user meta upon profile update
add_action('profile_update', 'update_custom_user_meta', 10, 2);

function remove_specific_style_tag() {
    // Start output buffering
    ob_start(function ($buffer) {
        // Use preg_replace to remove the specific style tag
        $buffer = preg_replace("/<style id='global-styles-inline-css'>.*?<\/style>/s", "", $buffer);
        return $buffer;
    });
}
add_action('wp_loaded', 'remove_specific_style_tag');

function end_output_buffering() {
    // End output buffering
    if (ob_get_length()) ob_end_flush();
}
add_action('shutdown', 'end_output_buffering');

function allow_svg_uploads($mimes) {
  $mimes['svg'] = 'image/svg+xml';
  return $mimes;
}
add_filter('upload_mimes', 'allow_svg_uploads');

// add_action('updated_user_meta', [$OrgActivities, 'notify_about_demo_request'], 10, 4);

function prevent_acf_update_value_boolean($value, $post_id, $field) {
  $field_name = 'Any_Time'; // Replace with your actual field name
  $existing_value = get_post_meta($post_id, $field_name, true);

  // Check if the new value is different from the existing value
  if ($existing_value !== '' && $existing_value !== null && $existing_value != $value) {
      // Set a transient to store the error message
      set_transient('acf_update_error_' . get_current_user_id(), 'You can update this value only once.', 30);

      // Return the existing value to prevent update
      return $existing_value;
  }

  // If the meta value doesn't exist or is the same, allow the new value to be saved
  return $value;
}
add_filter('acf/update_value/name=Any_Time', 'prevent_acf_update_value_boolean', 10, 3);
function display_acf_update_error_notice() {
// Get the error message transient
$message = get_transient('acf_update_error_' . get_current_user_id());

if ($message) {
    ?>
    <div class="notice notice-error is-dismissible">
        <p style="background-color: red;
color: white;"><?php echo $message; ?></p>
    </div>
    <?php
    // Delete the transient after displaying the message
    delete_transient('acf_update_error_' . get_current_user_id());
}
}
add_action('admin_notices', 'display_acf_update_error_notice');
/**
 * Switches the account based on the provided authentication code.
 *
 * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
 * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
 */
function switch_account($authCode)
{
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->switchAccount($authCode);
}
/**
 * Switches the account based on the provided authentication code.
 *
 * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
 * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
 */
function switch_virtual_account($authCode, $org_id)
{
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->switchVirtualAccount($authCode, $org_id);
}
/**
 * Get the account;s access token based on the provided authentication.
 *
 * @throws \Aws\Exception\AwsException If an error occurs during the account switching process.
 * @return array The response data including the session token, access token, refresh token, credentials type, and user existence status.
 */
function get_google_meet_access_token($user_id, $org_id)
{
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->getGoogleMeetAccessToken($user_id, $org_id);
}
/**
 * Retrieves the Cognito access token using the provided authorization code.
 *
 * @param string $authCode The authorization code.
 * @return array The response containing the access token.
 * @throws \Exception If an error occurs during the request.
 */
function get_cognito_access_token($authCode)
{
    $oauthModel = new V4\OauthModel();
    return $oauthModel->getCognitoAccessToken($authCode);
}
/**
 * It is used to save access token in db (table : wp_user_tokens)
 * Parameter(param) : $param = array (
 * 'id_token' => $response['id_token'],
 * 'access_token' => $response['access_token'],
 * 'refresh_token' => $response['refresh_token'],
 * 'token_expiry' => strtotime($expireson),
 * 'auth_code' => $auth_code,
 * 'user_id' => $user_id,
 * 'resource' => 'google'
 * )
 */
function save_auth_access_token($params)
{
  $oauthModel = new V4\OauthModel();
  return $oauthModel->saveAuthAccessToken($params);
}
/**
 * It is used to save access token in user meta 
 * Parameter(param) : $param = array (
 * 'id_token' => $response['id_token'],
 * 'access_token' => $response['access_token'],
 * 'refresh_token' => $response['refresh_token'],
 * 'token_expiry' => strtotime($expireson),
 * 'auth_code' => $auth_code,
 * 'user_id' => $user_id,
 * 'resource' => 'google'
 * )
 */
function save_virtual_auth_access($user_id, $params)
{
  $oauthModel = new V4\OauthModel();
  return $oauthModel->saveVirtualAuthAccess($user_id, $params);
}
/**
 * Saves the user data in Elasticsearch based on the given parameters.
 *
 * @param array $params An associative array containing the following keys:
 *                      - user_existance: A boolean indicating if the user exists or not.
 *                      - user_id: The ID of the user.
 *                      - role: The role of the user.
 *                      - user: The user object.
 *                      - basic_details: The basic details of the user.
 * @throws None
 * @return void
 */
function save_user_in_es($params)
{
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->saveUserInEs($params);
}
/**
 * Redirects the user to a specified URL based on the provided parameters.
 *
 * @param array $params An array containing the following parameters:
 *                      - 'org_redirect_url' (string): The URL to redirect the user to.
 *                      - 'org_encoded' (string): A flag indicating whether the URL is encoded.
 *                      - 'user_id' (int): The ID of the user.
 *                      - 'mobile_web_token' (string): The mobile web token.
 *
 * @return void
 */
function yuno_resources_redirection($params)
{
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->yunoResourcesRedirection($params);
}
function populate_academies_field( $field ) {
  // Reset choices
  $field['choices'] = array();

  // Check if an org ID is provided via POST request
  $org_id = isset($_POST['org_id']) ? intval($_POST['org_id']) : 0;
  if ($org_id) {
      // Get academies using get_post_meta
      $academies = get_post_meta($org_id, 'academies', true);

      if (!empty($academies) && is_array($academies)) {
          foreach ($academies as $academy_id) {
              $field['choices'][ $academy_id ] = get_the_title($academy_id);
          }
      } else {
          $field['choices'][''] = 'No academies found for this organization';
      }
  }
  return $field;
}
add_filter('acf/load_field/name=academies', 'populate_academies_field');

function get_org_id_from_academy($academy_id)
{
  global $Utility;
  return $Utility->get_org_id_from_academy($academy_id);
}
function get_org_admins_from_org($org_id)
{
  global $Utility;
  return $Utility->get_org_admins_from_org($org_id);
}

function check_user_virtual_classroom_permissions($userId){
    // Create an instance of OauthController to use its methods
    $oauthController = new V4\OauthController();
    return $oauthController->checkUserVirtualClassroomPermissions($userId);
}


add_filter('rest_post_dispatch', function ($result, $server, $request) {
  // Log the response for debugging
  //error_log(json_encode($server)."step 1: 5922: ".date('Y-m-d H:i:s').", post data: ".json_encode($request)." === ".json_encode($result). "\n\n", 3, ABSPATH.'error-logs/google_refresh4.log');
  // Modify the default response if it's forbidden
  if ($result->get_status() === 401) {
      return new WP_REST_Response(array(
          'code' => 401,
          'message' => 'Sorry, you are not allowed to do that.',
          'status' => 'rest_forbidden'
      ), 401);
  }

  return $result;
}, 10, 3);


  /**
   * Trigger Custom Event and store Event Data For Analytics.
   */


   // Define the log directory path
if (!defined('LOGSTASH_LOG_DIR')) {
  define('LOGSTASH_LOG_DIR', WP_CONTENT_DIR . '/custom-logs/');
}

// Ensure the log directory exists
if (!file_exists(LOGSTASH_LOG_DIR)) {
  mkdir(LOGSTASH_LOG_DIR, 0755, true);
}


/**
 * Log the event details to a file and handle old log cleanup.
 *
 * @param array $event_details Array containing event data.
 */
function custom_log_event($event_details) {
    // Validate required fields
    $required_keys = ['user_id', 'action', 'target', 'target_id', 'timestamp'];
    foreach ($required_keys as $key) {
        if (!isset($event_details[$key])) {
            error_log('Missing required log field: ' . $key);
            return; // Do not log incomplete data
        }
    }

    // Create the log file name based on the current date
    $log_file_name = LOGSTASH_LOG_DIR . 'log_' . date('Y-m-d') . '.log';

    // Prepare the log entry
    $log_entry = json_encode($event_details) . PHP_EOL;

    // Write to the log file
    file_put_contents($log_file_name, $log_entry, FILE_APPEND | LOCK_EX);

    // Clean up old log files
    custom_cleanup_old_logs();
}

/**
 * Delete log files older than 7 days.
 */
function custom_cleanup_old_logs() {
    foreach (glob(LOGSTASH_LOG_DIR . 'log_*.log') as $file) {
        if (filemtime($file) < strtotime('-7 days')) {
            unlink($file);
        }
    }
}

/**
 * Trigger the custom logging hook.
 *
 * @param array $event_details Event details to log.
 */
function trigger_custom_event($event_details) {
    custom_log_event($event_details);
}


// function trigger_custom_event($event_type, $event_id, $index, $timestamp) {
//   /**
//    * Trigger Hook with Event Data
//    */
//   do_action('fetch_and_store_event', $event_type, $event_id, $index, $timestamp);
// }
// add_action('fetch_and_store_event', 'fetch_and_process_event_data', 10, 3);

// // Step 1: Define Hook


// // Step 2: Hook Listener
// add_action('fetch_and_process_event_data', 'fetch_process_store_event', 10, 3);

// function fetch_process_store_event($event_type, $event_id, $index, $timestamp) {
//   $es_host       = ELASTIC_SEARCH_END_URL; // Replace with your Elasticsearch endpoint
//   $source_index  = $index; // Dynamic index name based on event type
//   $target_index  = 'event-analytics'; // Fixed target index

//   // Step 3: Fetch Event Data from Source Index
//   $query = [
//       "query" => [
//           "bool" => [
//               "must" => [
//                   ["match" => ["_id" => $event_id]]
//               ]
//           ]
//       ]
//   ];

//   $fetch_url = "$es_host/$source_index/_search";
//   $response  = wp_remote_post($fetch_url, [
//       'method'  => 'POST',
//       'headers' => ['Content-Type' => 'application/json'],
//       'body'    => json_encode($query),
//       'timeout' => 10,
//   ]);

//   if (is_wp_error($response)) {
//       error_log('Error fetching event: ' . $response->get_error_message());
//       return;
//   }

//   $body    = wp_remote_retrieve_body($response);
//   $results = json_decode($body, true);

//   if (empty($results['hits']['hits'])) {
//       error_log("No event found with ID: $event_id in index: $source_index");
//       return;
//   }

//   $source_event = $results['hits']['hits'][0]['_source']['data']['details'];

//   // Step 4: Transform Event Data for Target Index
//   $event_data = [
//       'event_id'     => $event_id,
//       'event_type'   => $event_type,
//       'timestamp'    => $timestamp,
//       'user'         => [
//           'user_id' => $source_event['user_id'],
//           'name'    => $source_event['name'],
//           'image'   => $source_event['image']
//       ],
//       'target'       => [
//           'target_type' => $event_type,
//           'target_id'   => $source_event['batch_id'],
//           'target_name' => $source_event['batch_name']
//       ],
//       'event_details' => [
//           'batch' => [
//               'batch_id' => $source_event['batch_id']
//           ],
//           'instructor' => [
//               'instructor_name'  => $source_event['instructor_name'],
//               'instructor_image' => $source_event['instructor_image']
//           ],
//           'course' => [
//               'course_id'    => $source_event['course_id'],
//               'course_title' => $source_event['course_name']
//           ],
//           'academy' => [
//               'academy_id'       => $source_event['parent_cat_id'],
//               'academy_name'     => $source_event['category'],
//               'academy_fav_icon' => "" // No icon data available
//           ],
//           'Org' => [
//               'org_id'       => $source_event['parent_cat_id'],
//               'org_name'     => "Yuno Learning", // Static data, replace if dynamic
//               'org_fav_icon' => ""
//           ],
//           'learner' => [
//               'id'   => $source_event['user_id'],
//               'name' => $source_event['name']
//           ]
//       ]
//   ];

//   // Step 5: Store Transformed Data in Target Index
//   $store_url = "$es_host/$target_index/_doc/$event_id";
//   $store_response = wp_remote_post($store_url, [
//       'method'  => 'POST',
//       'headers' => ['Content-Type' => 'application/json'],
//       'body'    => json_encode($event_data),
//       'timeout' => 10,
//   ]);

//   if (is_wp_error($store_response)) {
//       error_log('Error storing event: ' . $store_response->get_error_message());
//       return;
//   }

//   error_log("Event $event_id successfully stored in $target_index.");
// }

// add_action('init', function () {
//   $event_details = [
//       'user_id'   => 11043,
//       'action'    => 'enrollment',
//       'target'    => 'enrollment',
//       'target_id' => 4392,
//       'timestamp' => current_time('mysql')
//   ];
//   trigger_custom_event($event_details);
// });

/**
 * This function generates a random nonce value for the Content-Security-Policy
 * header. The nonce is generated once per request and is used to specify
 * allowed inline scripts and styles.
 *
 * @return string The generated nonce value.
 */
function generate_csp_nonce() {
  if (!defined('CSP_NONCE')) {
      // Generate once per request
      define('CSP_NONCE', base64_encode(random_bytes(16)));
  }
  return CSP_NONCE;
}
//To secure your WordPress site according to OWASP (Open Web Application Security Project) best practices, you need to implement specific HTTP security headers. These headers help protect against common vulnerabilities like XSS (Cross-Site Scripting), clickjacking, content sniffing, and more.
add_action('send_headers',[$OwaspscanActivities, 'fun_send_headers'],100);
/**
 * to remove _link from default rest apis 
 */

 add_filter('rest_pre_echo_response',[$OwaspscanActivities, 'fun_pre_echo_response'], 10, 3);

function add_nonce_to_all_inline_styles($buffer) {
  $nonce = generate_csp_nonce(); // Use the same nonce from your CSP header
  $buffer = preg_replace('/<style(?![^>]*nonce)/i', '<style nonce="' . esc_attr($nonce) . '"', $buffer);
  $buffer = preg_replace('/<script(?![^>]*nonce)/i', '<script nonce="' . esc_attr($nonce) . '"', $buffer);
  $buffer = preg_replace('/<(div|span)\s+([^>]*)style=["\'][^"\']*["\']/', '<$1 $2', $buffer);
  return $buffer;
}

function start_output_buffer() {
  ob_start('add_nonce_to_all_inline_styles');
}

add_action('template_redirect', 'start_output_buffer');

function restrict_specific_apis_by_domain($result, $server, $request) {
    // List of allowed domains
    $allowed_domains = [
        'dev.yunolearning.com',
        'local.yunolearning.com',
        'vc.dev.yunolearning.com'
    ];
    
    // List of restricted API patterns
    $restricted_patterns = [
        //'#^/yuno/v1/user/auth/token/\d+/\d+$#',  // Pattern for token API
    ];

    // Get the requested endpoint
    $requested_endpoint = urldecode($request->get_route());
    $requested_route = rtrim(strtok($requested_endpoint, '?'), '/');

    // Get the domain from different possible sources
    $referer = isset($_SERVER['HTTP_REFERER']) ? parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST) : '';
    $origin = isset($_SERVER['HTTP_ORIGIN']) ? parse_url($_SERVER['HTTP_ORIGIN'], PHP_URL_HOST) : '';
    $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';

    // Log for debugging
    error_log("API Access Check - " . date('Y-m-d H:i:s') . 
        "\nRoute: " . $requested_route . 
        "\nReferer: " . $referer . 
        "\nOrigin: " . $origin . 
        "\nHost: " . $host . "\n", 
        3, ABSPATH . 'error-logs/domain.log');

    // Check if the requested endpoint matches a restricted pattern
    foreach ($restricted_patterns as $pattern) {
        if (preg_match($pattern, $requested_route)) {
            // Check if the request is from an allowed domain
            // Allow if either the host, referer, or origin matches allowed domains
            if (!in_array($host, $allowed_domains) && 
                !in_array($referer, $allowed_domains) && 
                !in_array($origin, $allowed_domains)) {
                
                // Log the rejection
                error_log("API Access Denied - " . date('Y-m-d H:i:s') . 
                    "\nRoute: " . $requested_route . 
                    "\nHost: " . $host . 
                    "\nReferer: " . $referer . 
                    "\nOrigin: " . $origin . "\n", 
                    3, ABSPATH . 'error-logs/domain_denied.log');
                
                return new WP_Error(
                    'rest_forbidden', 
                    'Access denied: Unauthorized Domain', 
                    ['status' => 403]
                );
            }
        }
    }

    return $result;
}

// Hook into WordPress REST API
add_filter('rest_pre_dispatch', 'restrict_specific_apis_by_domain', 10, 3);

//keep this at end
require_once('inc/global/logger/system_logs.php');

// Include setup options
// require_once get_stylesheet_directory() . '/inc/setup-options.php';


/**
 * Get comprehensive user data from Elasticsearch
 * 
 * This function retrieves all data related to a user from multiple Elasticsearch indices
 * 
 * @param int $user_id The ID of the user
 * @return array An array containing all user data from different Elasticsearch indices
 */
function get_user_elasticsearch_data($user_id) {
    if (empty($user_id) || !is_numeric($user_id)) {
        return [
            'success' => false,
            'message' => 'Invalid user ID provided'
        ];
    }

    $result = [
        'success' => true,
        'user_id' => $user_id,
        'data' => []
    ];

    // Array of indices to check for user data
    $indices = [
        'signedup' => "signedup-{$user_id}",
        'instructorsignedup' => "instructorsignedup-{$user_id}",
        'learnersignedup' => "learnersignedup-{$user_id}",
        'counselorsignedup' => "counselorsignedup-{$user_id}"
    ];

    // Execute Elasticsearch queries for each index
    foreach ($indices as $index_name => $document_id) {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/{$index_name}/_doc/{$document_id}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            $result['data'][$index_name] = [
                'status' => 'error',
                'message' => $err
            ];
            continue;
        }

        $response_data = json_decode($response, true);
        
        // Check if document exists
        if (isset($response_data['found']) && $response_data['found'] === true) {
            $result['data'][$index_name] = [
                'status' => 'found',
                'document_id' => $document_id,
                'source' => $response_data['_source']
            ];
            
            // Extract organization details if available
            if ($index_name === 'instructorsignedup' && isset($response_data['_source']['data']['details'])) {
                $details = $response_data['_source']['data']['details'];
                $org_id = 0;
                $org_details = [];
                
                error_log("Processing organization details for user ID: $user_id");
                
                // Check direct org_id field
                if (!empty($details['org_id'])) {
                    $org_id = (int)$details['org_id'];
                    $org_details['direct_org_id'] = $org_id;
                    error_log("Found direct org_id: $org_id");
                }
                
                // Check details_from_org array
                if (!empty($details['details_from_org']) && is_array($details['details_from_org'])) {
                    $org_details['from_org'] = [];
                    foreach ($details['details_from_org'] as $idx => $org_detail) {
                        if (!empty($org_detail['org_id'])) {
                            $org_details['from_org'][] = [
                                'org_id' => (int)$org_detail['org_id'],
                                'position' => $idx,
                                'data' => $org_detail
                            ];
                            
                            error_log("Found org_id in details_from_org: " . $org_detail['org_id'] . " at position $idx");
                            
                            // Set the first org_id if not already set
                            if ($org_id === 0) {
                                $org_id = (int)$org_detail['org_id'];
                                error_log("Setting primary org_id to: $org_id from details_from_org");
                            }
                        }
                    }
                }
                
                // Fetch organization name(s) if we have org IDs
                if ($org_id > 0) {
                    // Get primary organization details
                    $org_data = get_option('yuno_organization_' . $org_id);
                    $org_name = !empty($org_data['name']) ? $org_data['name'] : 'Unknown Organization';
                    
                    error_log("Found organization: $org_name (ID: $org_id)");
                    
                    $org_details['primary_organization'] = [
                        'id' => $org_id,
                        'name' => $org_name,
                        'data' => $org_data
                    ];
                    
                    // Get related academies if any
                    $academies_query = [
                        "query" => [
                            "term" => [
                                "data.details.org_id" => $org_id
                            ]
                        ],
                        "_source" => ["data.details.id", "data.details.name"]
                    ];
                    
                    $curl = curl_init();
                    curl_setopt_array($curl, [
                        CURLOPT_PORT => ELASTIC_SEARCH_PORT,
                        CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/academies/_search",
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => "",
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 30,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => "POST",
                        CURLOPT_POSTFIELDS => json_encode($academies_query, JSON_UNESCAPED_SLASHES),
                        CURLOPT_HTTPHEADER => [
                            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                            "cache-control: no-cache",
                            "content-type: application/json"
                        ],
                    ]);
                    
                    $academy_response = curl_exec($curl);
                    $academy_err = curl_error($curl);
                    curl_close($curl);
                    
                    if (!$academy_err) {
                        $academies_data = json_decode($academy_response, true);
                        if (isset($academies_data['hits']['hits']) && !empty($academies_data['hits']['hits'])) {
                            $org_details['academies'] = array_map(function($hit) {
                                return [
                                    'id' => $hit['_source']['data']['details']['id'],
                                    'name' => $hit['_source']['data']['details']['name']
                                ];
                            }, $academies_data['hits']['hits']);
                            
                            error_log("Found " . count($org_details['academies']) . " academies for organization ID: $org_id");
                        } else {
                            error_log("No academies found for organization ID: $org_id");
                        }
                    } else {
                        error_log("Error fetching academies: $academy_err");
                    }
                } else {
                    error_log("No organization ID found for user ID: $user_id");
                }
                
                if (!empty($org_details)) {
                    $result['data']['organization'] = $org_details;
                }
            }
        } else {
            $result['data'][$index_name] = [
                'status' => 'not_found',
                'document_id' => $document_id
            ];
        }
    }

    // Get enrollments data for the user
    $enrollment_query = [
        "size" => 100,
        "query" => [
            "bool" => [
                "must" => [
                    [
                        "term" => [
                            "data.details.user_id" => $user_id
                        ]
                    ]
                ]
            ]
        ],
        "_source" => [
            "data.details.enrollment_id",
            "data.details.user_id",
            "data.details.user_name",
            "data.details.user_email",
            "data.details.batch_id",
            "data.details.batch_name",
            "data.details.course_id",
            "data.details.course_name",
            "data.details.instructor_id",
            "data.details.instructor_name",
            "data.details.enrollment_status",
            "data.details.enrolled_on",
            "data.details.batch_end_date",
            "data.details.batch_start_date",
            "data.details.personalisation",
            "data.details.academy_id",
            "data.details.org_id"  // Added org_id to source fields
        ]
    ];

    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_PORT => ELASTIC_SEARCH_PORT,
        CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/batchenrollmentevent/_search",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => "",
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => json_encode($enrollment_query, JSON_UNESCAPED_SLASHES),
        CURLOPT_HTTPHEADER => [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache",
            "content-type: application/json"
        ],
    ]);

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);

    if (!$err) {
        $enrollments_data = json_decode($response, true);
        if (isset($enrollments_data['hits']['hits']) && !empty($enrollments_data['hits']['hits'])) {
            $result['data']['enrollments'] = [
                'status' => 'found',
                'count' => count($enrollments_data['hits']['hits']),
                'items' => array_map(function($hit) {
                    $details = $hit['_source']['data']['details'];
                    $enrollment_data = [
                        'id' => $hit['_id'],
                        'details' => $details
                    ];
                    
                    // Add organization name if org_id exists
                    if (!empty($details['org_id'])) {
                        $org_id = (int)$details['org_id'];
                        $org_data = get_option('yuno_organization_' . $org_id);
                        $enrollment_data['organization'] = [
                            'id' => $org_id,
                            'name' => !empty($org_data['name']) ? $org_data['name'] : 'Unknown Organization'
                        ];
                        error_log("Found enrollment org_id: $org_id for enrollment ID: " . $details['enrollment_id']);
                    } else {
                        error_log("No org_id found for enrollment ID: " . $details['enrollment_id']);
                    }
                    
                    return $enrollment_data;
                }, $enrollments_data['hits']['hits'])
            ];
        } else {
            $result['data']['enrollments'] = [
                'status' => 'not_found',
                'count' => 0,
                'items' => []
            ];
        }
    } else {
        $result['data']['enrollments'] = [
            'status' => 'error',
            'message' => $err
        ];
    }

    // Get batches where the user is an instructor
    if (isset($result['data']['instructorsignedup']) && $result['data']['instructorsignedup']['status'] === 'found') {
        $instructor_id = $user_id; // Create a local variable for use in the closure
        $batch_query = [
            "query" => [
                "nested" => [
                    "path" => "data.details.batch_details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "term" => [
                                        "data.details.batch_details.instructor_id" => $instructor_id
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            "_source" => [
                "data.details.record_id",
                "data.details.title",
                "data.details.batch_details",
                "data.details.org_id"  // Added org_id to source fields
            ],
            "size" => 100
        ];

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/course/_search",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($batch_query, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => [
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ],
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if (!$err) {
            $batches_data = json_decode($response, true);
            if (isset($batches_data['hits']['hits']) && !empty($batches_data['hits']['hits'])) {
                $result['data']['instructor_batches'] = [
                    'status' => 'found',
                    'count' => count($batches_data['hits']['hits']),
                    'items' => array_map(function($hit) use ($instructor_id) {
                        $course_data = [
                            'id' => $hit['_id'],
                            'course_id' => $hit['_source']['data']['details']['record_id'],
                            'course_title' => $hit['_source']['data']['details']['title']
                        ];
                        
                        // Add organization information if available
                        if (!empty($hit['_source']['data']['details']['org_id'])) {
                            $org_id = (int)$hit['_source']['data']['details']['org_id'];
                            $org_data = get_option('yuno_organization_' . $org_id);
                            $course_data['organization'] = [
                                'id' => $org_id,
                                'name' => !empty($org_data['name']) ? $org_data['name'] : 'Unknown Organization'
                            ];
                            error_log("Found batch org_id: $org_id for course: " . $course_data['course_title']);
                        } else {
                            error_log("No org_id found for course: " . $course_data['course_title']);
                        }
                        
                        // Filter batches for this instructor
                        $course_data['batches'] = array_filter(
                            $hit['_source']['data']['details']['batch_details'] ?? [], 
                            function($batch) use ($instructor_id) {
                                return isset($batch['instructor_id']) && $batch['instructor_id'] == $instructor_id;
                            }
                        );
                        
                        return $course_data;
                    }, $batches_data['hits']['hits'])
                ];
            } else {
                $result['data']['instructor_batches'] = [
                    'status' => 'not_found',
                    'count' => 0,
                    'items' => []
                ];
            }
        } else {
            $result['data']['instructor_batches'] = [
                'status' => 'error',
                'message' => $err
            ];
        }
    }

    return $result;
}

