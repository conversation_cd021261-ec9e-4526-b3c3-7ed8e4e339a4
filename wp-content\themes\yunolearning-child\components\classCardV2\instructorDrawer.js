Vue.component("yuno-instructor-drawer", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
    <div class="wrapper">
        <div class="hasBG">
			<div class="cardContentWrapper">
				<div class="videoWrapper" v-if="isPast && data?.recording?.url != ''">
					<template v-if="videoList.loading">
						<b-skeleton height="300px"></b-skeleton>
					</template>
					<template v-if="videoList.success && videoList.error === null && videoEmbed != ''">
						<div class="videoLPPlayer" v-html="videoEmbed"></div>
					</template>
				</div>
				<div class="classStatus" v-if="!isPast && data.temporal_status != 'upcoming'">
					<span class="dot"></span>
					<span>{{ data.temporal_status }}</span>
				</div>
				<div>
					<span :class="['classType mr-2', 'onSurfaceVariant', data.type == 'WEBINAR' ? 'lightPeach' : 'bgLightBlue']">
						{{ data.type }}
					</span>
					<span 
						class="classType bgLightGreen" 
						v-if="isPast"
					>
						{{ data?.recording?.url ? 'RECORDING AVAILABLE' : 'RECORDING NOT AVAILABLE' }}
					</span>
				</div>
				<span class="classTitle headline5">{{ data.class_title.title }}</span>
				<div class="d-flex align-items-center">
					<span class="onSurfaceVariant subtitle2 noBold"> {{ formattedDateTime }}</span>
					<span class="separator">|</span>
					<span v-if="hasDuration" class="onSurfaceVariant subtitle2 noBold">{{ totalDuration }} Minutes</span>
				</div>
				<div class="classDetails">
					<span class="onSurfaceVariant subtitle2">BATCH: {{ data.batch.title }}</span>
					<span class="separator">|</span>
					<span class="onSurfaceVariant subtitle2">ID: {{ data.batch.id }}</span> 
					<span class="separator">|</span>
					<div class="d-flex" v-if="isPast && data?.recording?.url != ''">
						<div class="recordingDuration">
							<div class="playIcon">
								<span class="material-icons">play_arrow</span>
							</div>
						<span>{{ data?.recording?.duration }} minutes</span>
						</div>
					</div>
				</div>
				<template v-if="validEnrollments.length">
					<ul class="learnerList pl-3">
						<li v-for="(enrollment, index) in limitedEnrollments" :key="index">
							<figure class="learnerMapped">
								<img v-if="enrollment.image.url != ''" :src="enrollment.image.url" alt="enrollment.image.alt_text" />
								<span v-else class="material-icons">account_circle</span>
							</figure>
						</li>
						<span class="onSurfaceVariant subtitle2 noBold pl-2">
							{{ totalEnrollments }}
						</span>
						<template v-if="isPast">
							<span class="onSurfaceVariant subtitle2 noBold pl-2">({{ progressValue }}%)</span>
							<b-progress
								:type="attendanceClass"
								:value="progressValue"
								style="flex-grow: 1;padding-left: 10px;"
							>
							</b-progress>
						</template>
					</ul>
					</template>
				</template>
			</div>
			<div class="buttonWrapper" v-if="!isPast">
				<div class="cta">
					<b-button 
						tag="a"
						class="button yunoPrimaryCTA"
						:disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
						:href="data.virtual_classroom.meeting_url"
					>
						Launch Class
					</b-button>
				</div>
			</div>
		</div>
        <div class="courseDetails d-flex flex-column">
            <span class="onSurfaceVariant overline pb-2">COURSE</span>
            <span class="subtitle1 onSurface">{{ data.course.title }}</span>
            <div class="hasFlex">
                <span class="academyLogo">
                    <img :src="data.academy.logo_url.url" :alt="data.academy.logo_url.alt_text" />
                </span>
                <span class="favIcon onSurface">{{ formattedAcademyName }}</span>
            </div>
        </div>
        <div class="dropdownWrapper pt-5" v-if="!isPast">
            <b-collapse 
                class="guestLink" 
                animation="slide" 
                :open="false"
                aria-id="contentIdForA11y3"
                
            >
				<template #trigger="props">
					<div
						class="card-header"
						role="button"
						aria-controls="contentIdForA11y3"
						:aria-expanded="props.open"
					>
						<p class="card-header-title">Open guest link</p>
						<a class="card-header-icon"><span class="material-icons headline6 onSurface">{{ props.open ? 'expand_less' : 'expand_more' }}</span></a>
					</div>
				</template>
				<div class="content">
					<div class="urlWrapper">
						<div class="url subtitle2 noBold">
							{{ data.guest_url }}
						</div>
						<a
							class="copyGuestUrl caption1"
							@click="copyGuestUrl"
						>
							Copy Link
						</a>
					</div>
				</div>
            </b-collapse>
        </div>
		<div class="pt-4" v-if="false">
			<div class="d-flex justify-content-between align-items-baseline">
				<span class="onSurface subtitle2 ">Attendance</span>
				<div class="d-flex" style="gap:10px">
					<b-field>                                     
						<b-select 
							placeholder="All attendance" size="is-small">
							<option value="">All attendance</option>
							<option value="3">1</option>
							<option value="2">2</option>
							<option value="3">3</option>
						</b-select>
					</b-field>
					<b-field>                                     
						<b-select 
							placeholder="All rated" size="is-small">
							<option value="">All rated</option>
							<option value="3">1</option>
							<option value="2">2</option>
							<option value="3">3</option>
						</b-select>
					</b-field>
				</div>
			</div>
			<div class="instructorLearners">       
				<ul>
					<li v-for="i in 5" :key="i">
						<!-- Profile Section -->
						<div class="learnerProfile">
							<img :src="data.instructor.user.image_url" alt="Profile Picture" />
							<span class="onSurfaceVariant subtitle2 noBold">Pooja bajaj</span>
						</div>
						<div class="learnerAttendance">
							<span class="onSurfaceVariant subtitle2 noBold">36 minutes (59.8%)</span>
							<b-progress
								type="is-danger"
								:value="59"
							>
							</b-progress>
						</div>
						<div class="learnerRating">
							<span class="material-icons subtitle3 pb-1" style="color:#FFD700">star</span>
							<span class="onSurfaceVariant subtitle2 noBold">1 of 5</span>
						</div>
					</li>
				</ul>
			</div>
        </div>
    </div>
  `,
  data() {
    return {
      defaultImage:
        this.$store.state.themeURL + "/assets/images/instructor profile.png",
    };
  },
  computed: {
    ...Vuex.mapState(["userInfo", "userRole", "videoList"]),

    videoEmbed() {
      return this.videoList.data.videoEmbed;
    },

    isPast() {
      return this.data.temporal_status === "past";
    },
    attendanceClass() {
      const attendanceCount = this.data?.attendance_of_each?.length || 0;
      const enrollmentCount = this.data?.enrollments.length || 1; // Avoid division by zero
      const p = (attendanceCount * 100) / enrollmentCount;
      if (p <= 30) return "is-red";
      if (p <= 50) return "is-orange";
      if (p <= 70) return "is-yellow";
      if (p <= 80) return "is-lightGreen";
      if (p <= 90) return "is-blue";
      return "is-green";
    },
    formattedDateTime() {
      const time =
        this.data.temporal_status === "past"
          ? this.data?.actual?.start?.time
          : this.data?.scheduled?.start?.time;

      if (!time || time.trim() === "") {
        return "";
      }

      const dateTime = new Date(time);

      // Check if the date is valid
      if (isNaN(dateTime.getTime())) {
        return "Invalid date format";
      }

      try {
        const date = dateTime.toLocaleDateString("en-US", {
          day: "2-digit",
          month: "long",
          year: "numeric",
        });
        const timeStr = dateTime.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        });
        return `${date} - ${timeStr}`;
      } catch (error) {
        console.error("Error formatting date:", error);
        return "Error formatting date";
      }
    },
	validEnrollments() {
  		return this.data?.enrollments?.filter(e => e.id > 0) || [];
	},
    limitedEnrollments() {
      return this.validEnrollments.slice(0, 5) || [];
    },
    totalEnrollments() {
      if (!this.isPast) {
        return this.validEnrollments.length > 0
          ? `${this.validEnrollments.length} Students enrolled`
          : "";
      } else {
        const attended = this.data?.attendance_of_each?.length || 0;
        const total = this.validEnrollments.length || 0;
        return `${attended} of ${total} attended`;
      }
    },
    totalDuration() {
      if (this.data.temporal_status !== "past") {
        return this.data.scheduled.duration;
      } else {
        return this.data.actual.duration;
      }
    },
    hasDuration() {
      if (this.data.temporal_status !== "past") {
        return this.data?.scheduled?.duration > 0;
      } else {
        return this.data?.actual?.duration > 0;
      }
    },
    progressValue() {
      const attendanceCount = this.data?.attendance_of_each?.length || 0;
      const enrollmentCount = this.data?.enrollments?.length || 1;
      const percentage = (attendanceCount * 100) / enrollmentCount;
      return percentage > 0 ? Math.round(percentage) : 0;
    },
    formattedAcademyName() {
      // Check if academy name exists and is not empty
      if (!this.data?.academy?.name) {
        return "";
      }
      return this.data.academy.name
        .split("-") // Convert into array by splitting at '-'
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
        .join(" "); // Join back as a string with spaces
    },
  },
  methods: {
    copyGuestUrl() {
      navigator.clipboard
        .writeText(this.data.guest_url)
        .then(() => {
          this.$buefy.toast.open({
            message: "Link copied to clipboard!",
            type: "is-success",
            position: "is-bottom",
            duration: 2000,
          });
        })
        .catch((err) => {
          console.error("Failed to copy:", err);
        });
    },
  },
});
