<?php
return [
	'id' => 'integer', // unique number of batch example 2814
	'title' => 'string', // title of batch example xor-batch
	'temporal_state' => 'string', // Whether the batch is in the past or upcoming/ongoing enum ['PAST', 'UPCOMINGONGOING'] example xor-batch
	'instructor' => 'Refer#User_Minimal', // Which instructor is teaching this batch
	'checkout_url' => 'uri', // Unique URL of the batch that can be shared with learners so they can enroll. It's the same as a checkout page example https://dev.yunolearning.com/batch/test/
	'created_by' => 'Refer#User_Minimal', // Which user created this batch
	'is_active' => 'boolean', // Active batch is a batch that is ongoing (end date is > current date) and has at least 1 enrollment i.e. enrollment_count >= 1  default True
	'is_locked' => 'boolean', // An active batch can be locked. More enrollments cannot be done even if there are vacancies
	'is_deleted' => 'boolean', // Deleted batch state - a batch that has been deleted cannot be considered for any further action except for display. A batch that has ended DOES NOT necessarily have to be deleted.
	'start_end' => // Start and end dates of batch. If the batch if of the "enrollment type" "rolling", display start and end dates are shown. Display start date is always tomorrow
    [
        'start_date' => 'Refer#Date_Time', // batch start date
		'end_date' => 'Refer#Date_Time', // end date of batch
		'display_start' => 'Refer#Date_Time', // display start date of batch
		'display_end' => 'Refer#Date_Time' // display end date of batch
    ],
	'class_days' => [
		[
			'day' => 'string', // days of week enum ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'] example WED
			'name' => 'string', // Name of the day such as "Wednesday" example Wednesday
			'available' => 'boolean', // MON = true; TUE = false; etc. default True
		]
	],
	'class_time' => [
		'name' => 'string', // Time is grouped in four values: morning, afternoon, evening and night enum ['MORNING', 'AFTERNOON', 'EVENING', 'NIGHT']
		'start_time' => 'Refer#Date_Time', // class start time
		'end_time' => 'Refer#Date_Time', // class end date
		'duration' => 'integer', // class duration example 30
	],
	'personalisation' => 'Refer#Personalisation', // personalisation of batch,
	'teaching_mode' => 'Refer#Teaching_Mode', // Whether classes of the batch will be online, in-person or both (hybrid)
	'seats' => [
		'max' => 'integer', // Max. seats allowed in the batch example 10
		'occupied' => 'integer', // Occupied seats i.e. number of active enrollments example 5
		'vacant' => 'integer', // Difference between max seats and occupied seats example 5
	],
	'in_crm' => [
		'platform' => 'string', // The CRM platform enum ['ZOHO', 'SALESFORCE']
		'batch_id' => 'string'
	],
	'course' => 'Refer#Course_Minimal', // Course component
	'active_enrollments' => [
        'Refer#Enrollment_Minimal'
    ], // Placeholder for enrollment object
	'place' => 'Refer#Place_Minimal', // Place at which this batch is at - when the teaching mode is either in-person or hybrid
	'classroom' => 'Refer#Classroom_Minimal', // When the teaching mode is either in-person or hybrid, which location-based classroom is this batch at
];
